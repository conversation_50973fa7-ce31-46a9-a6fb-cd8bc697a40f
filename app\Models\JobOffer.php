<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobOffer extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'company_id',
        'tags',
        'description',
        'prerequisites',
        'responsabilities',
        'position_type_id',
        'city',
        'country',
        'listedAt',
    ];

    protected $casts = [
        'tags' => 'array',
        'listedAt' => 'datetime',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function positionType()
    {
        return $this->belongsTo(PositionType::class);
    }
} 