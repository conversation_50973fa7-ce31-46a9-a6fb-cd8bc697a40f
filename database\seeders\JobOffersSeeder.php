<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use App\Models\Company;
use App\Models\PositionType;

class JobOffersSeeder extends Seeder
{
    public function run(): void
    {
        $offers = [
            [
                'title' => 'Service Designer',
                'company_name' => 'Acme Inc.',
                'location' => 'Paris, France',
                'description' => "Nous recherchons un designer de service talentueux pour rejoindre notre équipe. Vous serez responsable de la conception d'expériences utilisateur exceptionnelles pour nos clients. Vous travaillerez en étroite collaboration avec les équipes produit et développement pour créer des solutions innovantes.",
                'skills' => ["UX Design", "UI Design", "Figma", "Adobe XD", "Design Thinking"],
                'requirements' => [
                    "Diplôme en design, expérience utilisateur ou domaine connexe",
                    "3+ ans d'expérience en conception de services ou d'expériences utilisateur",
                    "Maîtrise des outils de conception comme Figma et Adobe XD",
                    "Excellentes compétences en communication et présentation",
                    "Capacité à travailler en équipe multidisciplinaire"
                ],
                'responsibilities' => [
                    "Concevoir des expériences utilisateur intuitives et engageantes",
                    "Réaliser des recherches utilisateurs et des tests d'utilisabilité",
                    "Créer des wireframes, des prototypes et des maquettes détaillées",
                    "Collaborer avec les équipes produit et développement",
                    "Présenter les concepts de design aux parties prenantes"
                ]
            ],
            [
                'title' => 'UX/UI Designer',
                'company_name' => 'Tech Solutions',
                'location' => 'Lyon, France',
                'description' => "Tech Solutions recherche un designer UX/UI passionné pour rejoindre notre équipe en pleine croissance. Vous serez chargé de créer des interfaces utilisateur attrayantes et fonctionnelles pour nos applications web et mobiles.",
                'skills' => ["UX Research", "Wireframing", "Prototyping", "Responsive Design", "HTML/CSS"],
                'requirements' => [
                    "Diplôme en design d'interaction, design graphique ou domaine connexe",
                    "2+ ans d'expérience en conception d'interfaces utilisateur",
                    "Portfolio démontrant des compétences en UX/UI design",
                    "Connaissance des principes de design responsive",
                    "Expérience avec les frameworks de design système"
                ],
                'responsibilities' => [
                    "Créer des wireframes et des prototypes interactifs",
                    "Concevoir des interfaces utilisateur intuitives et esthétiques",
                    "Collaborer avec les développeurs pour implémenter les designs",
                    "Participer aux sessions de recherche utilisateur",
                    "Maintenir et améliorer notre design système"
                ]
            ]
        ];

        $positionType = PositionType::where('code', 'CDI')->first();
        foreach ($offers as $offer) {
            [$city, $country] = array_map('trim', explode(',', $offer['location']));
            $company = Company::where('name', $offer['company_name'])->first();
            // Avoid duplicates
            $exists = DB::table('job_offers')
                ->where('title', $offer['title'])
                ->where('company_id', $company ? $company->id : null)
                ->where('city', $city)
                ->exists();
            if ($exists) continue;
            DB::table('job_offers')->insert([
                'title' => $offer['title'],
                'company_id' => $company ? $company->id : null,
                'tags' => json_encode($offer['skills']),
                'description' => $offer['description'],
                'prerequisites' => implode("\n", $offer['requirements']),
                'responsabilities' => implode("\n", $offer['responsibilities']),
                'position_type_id' => $positionType ? $positionType->id : null,
                'city' => $city,
                'country' => $country,
                'listedAt' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $extraOffers = [
            [
                'title' => 'Frontend Developer',
                'company_name' => 'Web Agency',
                'location' => 'Bordeaux, France',
                'description' => 'Développez des interfaces web modernes pour nos clients.',
                'skills' => ['React', 'JavaScript', 'CSS', 'HTML'],
                'requirements' => [
                    'Expérience avec React',
                    'Bonne connaissance du CSS',
                    'Capacité à travailler en équipe',
                ],
                'responsibilities' => [
                    'Développer des composants réutilisables',
                    'Collaborer avec les designers',
                ]
            ],
            [
                'title' => 'Backend Developer',
                'company_name' => 'Tech Solutions',
                'location' => 'Lille, France',
                'description' => 'Rejoignez notre équipe backend pour construire des API robustes.',
                'skills' => ['PHP', 'Laravel', 'MySQL'],
                'requirements' => [
                    'Expérience avec Laravel',
                    'Connaissance des bases de données',
                ],
                'responsibilities' => [
                    'Développer des API REST',
                    'Optimiser les requêtes SQL',
                ]
            ],
            [
                'title' => 'Product Manager',
                'company_name' => 'Acme Inc.',
                'location' => 'Paris, France',
                'description' => 'Gérez le cycle de vie de nos produits digitaux.',
                'skills' => ['Gestion de projet', 'Scrum', 'Communication'],
                'requirements' => [
                    'Expérience en gestion de produit',
                    'Bonne communication',
                ],
                'responsibilities' => [
                    'Définir la roadmap',
                    'Coordonner les équipes',
                ]
            ],
            [
                'title' => 'UI Designer',
                'company_name' => 'Design Studio',
                'location' => 'Marseille, France',
                'description' => 'Créez des interfaces élégantes et intuitives.',
                'skills' => ['Figma', 'Adobe XD', 'UI Design'],
                'requirements' => [
                    'Portfolio de projets UI',
                    'Créativité',
                ],
                'responsibilities' => [
                    'Créer des maquettes',
                    'Collaborer avec les développeurs',
                ]
            ],
            [
                'title' => 'QA Engineer',
                'company_name' => 'StartUp Co.',
                'location' => 'Lyon, France',
                'description' => 'Assurez la qualité de nos applications.',
                'skills' => ['Tests automatisés', 'Cypress', 'Jest'],
                'requirements' => [
                    'Expérience en QA',
                    'Connaissance des outils de test',
                ],
                'responsibilities' => [
                    'Écrire des tests',
                    'Automatiser les scénarios',
                ]
            ],
            [
                'title' => 'DevOps Engineer',
                'company_name' => 'Tech Solutions',
                'location' => 'Paris, France',
                'description' => 'Automatisez et optimisez nos déploiements.',
                'skills' => ['Docker', 'CI/CD', 'AWS'],
                'requirements' => [
                    'Expérience avec Docker',
                    'Connaissance des pipelines CI/CD',
                ],
                'responsibilities' => [
                    'Maintenir l\'infrastructure cloud',
                    'Mettre en place des pipelines',
                ]
            ],
            [
                'title' => 'Fullstack Developer',
                'company_name' => 'Web Agency',
                'location' => 'Lille, France',
                'description' => 'Développez des applications web complètes.',
                'skills' => ['Node.js', 'React', 'MongoDB'],
                'requirements' => [
                    'Expérience fullstack',
                    'Autonomie',
                ],
                'responsibilities' => [
                    'Développer frontend et backend',
                    'Participer aux revues de code',
                ]
            ],
            [
                'title' => 'Mobile Developer',
                'company_name' => 'Acme Inc.',
                'location' => 'Bordeaux, France',
                'description' => 'Créez des applications mobiles performantes.',
                'skills' => ['Flutter', 'iOS', 'Android'],
                'requirements' => [
                    'Expérience mobile',
                    'Connaissance Flutter',
                ],
                'responsibilities' => [
                    'Développer des apps mobiles',
                    'Assurer la maintenance',
                ]
            ],
            [
                'title' => 'Support Engineer',
                'company_name' => 'StartUp Co.',
                'location' => 'Marseille, France',
                'description' => 'Aidez nos clients à résoudre leurs problèmes techniques.',
                'skills' => ['Support', 'Communication', 'Résolution de problèmes'],
                'requirements' => [
                    'Patience',
                    'Bonne communication',
                ],
                'responsibilities' => [
                    'Répondre aux tickets',
                    'Documenter les solutions',
                ]
            ],
            [
                'title' => 'Data Analyst',
                'company_name' => 'Design Studio',
                'location' => 'Paris, France',
                'description' => 'Analysez les données pour guider nos décisions.',
                'skills' => ['SQL', 'Python', 'DataViz'],
                'requirements' => [
                    'Expérience en analyse de données',
                    'Maîtrise de SQL',
                ],
                'responsibilities' => [
                    'Créer des rapports',
                    'Analyser les tendances',
                ]
            ],
        ];

        foreach ($extraOffers as $offer) {
            [$city, $country] = array_map('trim', explode(',', $offer['location']));
            $company = Company::where('name', $offer['company_name'])->first();
            $positionType = PositionType::inRandomOrder()->first();
            // Avoid duplicates
            $exists = DB::table('job_offers')
                ->where('title', $offer['title'])
                ->where('company_id', $company ? $company->id : null)
                ->where('city', $city)
                ->exists();
            if ($exists) continue;
            DB::table('job_offers')->insert([
                'title' => $offer['title'],
                'company_id' => $company ? $company->id : null,
                'tags' => json_encode($offer['skills']),
                'description' => $offer['description'],
                'prerequisites' => implode("\n", $offer['requirements']),
                'responsabilities' => implode("\n", $offer['responsibilities']),
                'position_type_id' => $positionType ? $positionType->id : null,
                'city' => $city,
                'country' => $country,
                'listedAt' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
} 