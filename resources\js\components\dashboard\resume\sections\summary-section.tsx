import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Pencil } from "lucide-react"
import { ProfileModal } from "@/components/dashboard/resume/edit/profile-modal"
import { useAuth } from "@/hooks/use-auth"
import { useTranslation } from 'react-i18next'

interface SummarySectionProps {
  resumeData: any
  onUpdate: () => void
}

export function SummarySection({ resumeData, onUpdate }: SummarySectionProps) {
  const [profileModalOpen, setProfileModalOpen] = useState(false)
  const { isAuthenticated } = useAuth()
  const { t } = useTranslation()

  return (
    <div className="mb-8">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-medium">{t("Résumé")}</h3>
        {isAuthenticated && (
          <Button variant="ghost" size="sm" onClick={() => setProfileModalOpen(true)}>
            <Pencil className="mr-2 h-4 w-4" />
            {t("Modifier")}
          </Button>
        )}
      </div>
      <p className="text-sm text-muted-foreground">
        {resumeData.summary || t("Ajoutez un résumé pour vous présenter aux recruteurs.")}
      </p>

      {profileModalOpen && (
        <ProfileModal
          isOpen={profileModalOpen}
          onClose={() => setProfileModalOpen(false)}
          resume={resumeData}
          onSuccess={onUpdate}
        />
      )}
    </div>
  )
}
