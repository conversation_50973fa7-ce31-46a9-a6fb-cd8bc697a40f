import { IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useJobOffers } from '../context/job-offers-context'

export function JobOffersPrimaryButtons() {
  const { setOpen } = useJobOffers()

  return (
    <div className='flex items-center space-x-2'>
      <Button
        size='sm'
        onClick={() => setOpen('add')}
        className='h-8'
      >
        <IconPlus className='mr-2 h-4 w-4' />
        Ajouter une offre
      </Button>
    </div>
  )
}
