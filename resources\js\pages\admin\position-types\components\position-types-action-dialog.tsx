'use client'

import { useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PositionType } from '../data/schema'
import axios from 'axios'
import { router } from '@inertiajs/react'

const formSchema = z.object({
  code: z.string().min(1, 'Le code est requis'),
  name: z.string().min(1, 'Le nom est requis'),
})

type PositionTypeForm = z.infer<typeof formSchema>

interface Props {
  currentRow?: PositionType
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function PositionTypesActionDialog({ currentRow, open, onOpenChange }: Props) {
  const isEdit = !!currentRow
  const [loading, setLoading] = useState(false)

  const form = useForm<PositionTypeForm>({
    resolver: zodResolver(formSchema),
    defaultValues: isEdit
      ? {
          code: currentRow.code,
          name: currentRow.name,
        }
      : {
          code: '',
          name: '',
        },
  })

  const onSubmit = async (values: PositionTypeForm) => {
    setLoading(true)
    try {
      if (isEdit) {
        await axios.put(`/api/position-types/${currentRow.id}`, values)
        toast({
          title: 'Succès',
          description: 'Type de poste modifié avec succès',
        })
      } else {
        await axios.post('/api/position-types', values)
        toast({
          title: 'Succès',
          description: 'Type de poste créé avec succès',
        })
      }

      form.reset()
      onOpenChange(false)
      router.reload()
    } catch (error: any) {
      console.error('Error saving position type:', error)
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (!loading) {
          form.reset()
          onOpenChange(state)
        }
      }}
    >
      <DialogContent className='sm:max-w-lg'>
        <DialogHeader className='text-left'>
          <DialogTitle>{isEdit ? "Modifier le type de poste" : "Ajouter un type de poste"}</DialogTitle>
          <DialogDescription>
            {isEdit ? "Modifiez les informations du type de poste." : "Créez un nouveau type de poste."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id='position-type-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='space-y-4'
          >
            <FormField
              control={form.control}
              name='code'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Code *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='CDI, CDD, STAGE...'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Contrat à durée indéterminée'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type='submit'
            form='position-type-form'
            disabled={loading}
          >
            {loading ? 'Enregistrement...' : (isEdit ? 'Modifier' : 'Créer')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
