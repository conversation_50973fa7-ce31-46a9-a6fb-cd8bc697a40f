'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { JobOffer } from '../data/schema'
import axios from 'axios'
import { router } from '@inertiajs/react'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: JobOffer
}

export function JobOffersDeleteDialog({ open, onOpenChange, currentRow }: Props) {
  const [deleteInput, setDeleteInput] = useState('')
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    if (deleteInput !== currentRow.title) {
      toast({
        title: 'Erreur',
        description: 'Le titre saisi ne correspond pas',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    try {
      await axios.delete(`/api/job-offers/${currentRow.id}`)
      toast({
        title: 'Succès',
        description: 'Offre d\'emploi supprimée avec succès',
      })
      onOpenChange(false)
      setDeleteInput('')
      router.reload()
    } catch (error: any) {
      console.error('Error deleting job offer:', error)
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={(state) => {
        if (!loading) {
          setDeleteInput('')
          onOpenChange(state)
        }
      }}
      title="Supprimer l'offre d'emploi"
      desc="Cette action est irréversible. L'offre d'emploi sera définitivement supprimée."
      confirmText={loading ? 'Suppression...' : 'Supprimer'}
      handleConfirm={handleDelete}
      disabled={deleteInput !== currentRow.title || loading}
      destructive={true}
    >
      <Alert variant='destructive'>
        <IconAlertTriangle className='h-4 w-4' />
        <AlertTitle>Attention !</AlertTitle>
        <AlertDescription>
          Cette action supprimera définitivement l&apos;offre d&apos;emploi &quot;{currentRow.title}&quot;.
        </AlertDescription>
      </Alert>
      
      <div className='space-y-2'>
        <Label htmlFor="delete-input">
          Pour confirmer, tapez le titre de l&apos;offre : <strong>{currentRow.title}</strong>
        </Label>
        <Input
          id="delete-input"
          value={deleteInput}
          onChange={(e) => setDeleteInput(e.target.value)}
          placeholder="Tapez le titre de l'offre"
          disabled={loading}
        />
      </div>
    </ConfirmDialog>
  )
}
