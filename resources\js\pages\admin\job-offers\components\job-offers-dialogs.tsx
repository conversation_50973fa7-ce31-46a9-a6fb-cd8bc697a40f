import { useJobOffers } from '../context/job-offers-context'
import { JobOffersActionDialog } from './job-offers-action-dialog'
import { JobOffersDeleteDialog } from './job-offers-delete-dialog'

export function JobOffersDialogs() {
  const { open, setOpen, currentRow } = useJobOffers()

  return (
    <>
      <JobOffersActionDialog
        key={`action-${currentRow?.id || 'new'}`}
        currentRow={open === 'edit' ? currentRow! : undefined}
        open={open === 'add' || open === 'edit'}
        onOpenChange={(state) => !state && setOpen(null)}
      />

      {currentRow && (
        <JobOffersDeleteDialog
          key={`delete-${currentRow.id}`}
          open={open === 'delete'}
          onOpenChange={(state) => !state && setOpen(null)}
          currentRow={currentRow}
        />
      )}
    </>
  )
}
