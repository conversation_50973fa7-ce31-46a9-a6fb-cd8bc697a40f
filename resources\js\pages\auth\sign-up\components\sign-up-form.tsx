import { HTMLAttributes, useState, useEffect } from 'react'
import { Link, useForm } from '@inertiajs/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/password-input'
import { Label } from '@/components/ui/label'
import InputError from '@/components/InputError'
import axios from 'axios'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface SignUpFormProps extends HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function SignUpForm({ className, ...props }: SignUpFormProps) {
  const { data, setData, post, processing, errors, reset } = useForm({
    name: '',
    username: '',
    email: '',
    password: '',
    password_confirmation: '',
  });

  const [usernameStatus, setUsernameStatus] = useState<{
    checking: boolean;
    available: boolean | null;
    message: string;
  }>({
    checking: false,
    available: null,
    message: '',
  });

  const checkUsernameAvailability = async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameStatus({
        checking: false,
        available: null,
        message: '',
      });
      return;
    }

    setUsernameStatus(prev => ({ ...prev, checking: true }));

    try {
      const response = await axios.post('/check-username', { username });
      setUsernameStatus({
        checking: false,
        available: response.data.available,
        message: response.data.message,
      });
    } catch (error) {
      setUsernameStatus({
        checking: false,
        available: false,
        message: 'Erreur lors de la vérification du nom d\'utilisateur.',
      });
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (data.username) {
        checkUsernameAvailability(data.username);
      }
    }, 500); // Debounce for 500ms

    return () => clearTimeout(timeoutId);
  }, [data.username]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    post(route('register'), {
      preserveScroll: true,
      onSuccess: () => {
        reset('password', 'password_confirmation');
      },
    });
  };

  return (
    <div className={cn('grid gap-4', className)} {...props}>
      <form onSubmit={handleSubmit} noValidate>
        <div className='grid gap-4'>
          <div className='space-y-1'>
            <Label htmlFor="name" className="text-sm font-medium">Nom complet *</Label>
            <Input
              id="name"
              placeholder="Entrez votre nom"
              autoComplete="name"
              value={data.name}
              onChange={(e) => setData('name', e.target.value)}
            />
            <InputError message={errors.name} className="mt-2" />
          </div>

          <div className='space-y-1'>
            <Label htmlFor="username" className="text-sm font-medium">Nom d'utilisateur *</Label>
            <div className="relative">
              <Input
                id="username"
                placeholder="nomutilisateur"
                autoComplete="username"
                value={data.username}
                onChange={(e) => setData('username', e.target.value)}
                className={cn(
                  "pr-32",
                  usernameStatus.available === true && "border-green-500 focus:border-green-500",
                  usernameStatus.available === false && "border-red-500 focus:border-red-500"
                )}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <div className="flex items-center space-x-2">
                  {usernameStatus.checking && (
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  )}
                  {!usernameStatus.checking && usernameStatus.available === true && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {!usernameStatus.checking && usernameStatus.available === false && (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm text-muted-foreground">@afeq.tn</span>
                </div>
              </div>
            </div>
            {usernameStatus.message && (
              <p className={cn(
                "text-xs mt-1",
                usernameStatus.available ? "text-green-600" : "text-red-600"
              )}>
                {usernameStatus.message}
              </p>
            )}
            <InputError message={errors.username} className="mt-2" />
          </div>

          <div className='space-y-1'>
            <Label htmlFor="email" className="text-sm font-medium">E-mail *</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              autoComplete="email"
              value={data.email}
              onChange={(e) => setData('email', e.target.value)}
            />
            <InputError message={errors.email} className="mt-2" />
          </div>

          <div className='space-y-1'>
            <Label htmlFor="password" className="text-sm font-medium">Mot de passe *</Label>
            <PasswordInput
              id="password"
              placeholder="Créez un mot de passe"
              autoComplete="new-password"
              value={data.password}
              onChange={(e) => setData('password', e.target.value)}
            />
            <InputError message={errors.password} className="mt-2" />
          </div>

          <div className='space-y-1'>
            <Label htmlFor="password_confirmation" className="text-sm font-medium">Confirmer le mot de passe *</Label>
            <PasswordInput
              id="password_confirmation"
              placeholder="Confirmez votre mot de passe"
              autoComplete="new-password"
              value={data.password_confirmation}
              onChange={(e) => setData('password_confirmation', e.target.value)}
            />
            <InputError message={errors.password_confirmation} className="mt-2" />
          </div>

          <Button
            className='mt-4 bg-primary'
            type="submit"
            disabled={processing || usernameStatus.checking || usernameStatus.available === false}
          >
            {processing ? 'Création du compte...' : 'S\'inscrire'}
          </Button>

          <div className="mt-2 text-center">
            <p className="text-xs text-muted-foreground">
              Vous avez déjà un compte? <Link href={route('login')} className="text-primary hover:underline">Se connecter</Link>
            </p>
          </div>
        </div>
      </form>
    </div>
  );
}

export default SignUpForm;
