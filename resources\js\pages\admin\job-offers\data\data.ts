import { IconBuilding, IconMapPin, IconCalendar, IconTag } from '@tabler/icons-react'

export const jobOfferStatuses = [
  {
    label: 'Active',
    value: 'active',
    icon: IconTag,
  },
  {
    label: 'Inactive',
    value: 'inactive',
    icon: IconTag,
  },
] as const

export const countries = [
  {
    label: 'France',
    value: 'France',
    icon: IconMapPin,
  },
  {
    label: 'Belgique',
    value: 'Belgique',
    icon: IconMapPin,
  },
  {
    label: 'Suisse',
    value: 'Suisse',
    icon: IconMapPin,
  },
  {
    label: 'Canada',
    value: 'Canada',
    icon: IconMapPin,
  },
] as const

export const cities = [
  {
    label: 'Paris',
    value: 'Paris',
    icon: IconMapPin,
  },
  {
    label: 'Lyon',
    value: 'Lyon',
    icon: IconMapPin,
  },
  {
    label: 'Marseille',
    value: 'Marseille',
    icon: IconMapPin,
  },
  {
    label: 'Bordeaux',
    value: 'Bordeaux',
    icon: IconMapPin,
  },
  {
    label: 'Lille',
    value: '<PERSON>',
    icon: IconMapPin,
  },
] as const
