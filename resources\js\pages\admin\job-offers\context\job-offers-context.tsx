import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { JobOffer } from '../data/schema'

type JobOffersDialogType = 'add' | 'edit' | 'delete'

interface JobOffersContextType {
  open: JobOffersDialogType | null
  setOpen: (str: JobOffersDialogType | null) => void
  currentRow: JobOffer | null
  setCurrentRow: React.Dispatch<React.SetStateAction<JobOffer | null>>
}

const JobOffersContext = React.createContext<JobOffersContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function JobOffersProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<JobOffersDialogType>(null)
  const [currentRow, setCurrentRow] = useState<JobOffer | null>(null)

  return (
    <JobOffersContext.Provider value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </JobOffersContext.Provider>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useJobOffers = () => {
  const jobOffersContext = React.useContext(JobOffersContext)

  if (!jobOffersContext) {
    throw new Error('useJobOffers has to be used within <JobOffersContext>')
  }

  return jobOffersContext
}
