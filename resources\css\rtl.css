/* RTL Styles */
html[dir="rtl"] {
  /* Text alignment */
  text-align: right;
}

/* Dashboard sidebar RTL adjustments */
html[dir="rtl"] [data-sidebar="sidebar"] {
  /* Ensure text alignment in sidebar is right-aligned */
  text-align: right;
}

/* Adjust sidebar group labels for RTL */
html[dir="rtl"] [data-sidebar="group-label"] {
  flex-direction: row-reverse;
}

/* Adjust sidebar menu badges for RTL */
html[dir="rtl"] [data-sidebar="menu-badge"] {
  right: auto;
  left: 1rem;
}

/* Adjust sidebar menu actions for RTL */
html[dir="rtl"] [data-sidebar="menu-action"] {
  right: auto;
  left: 1rem;
}

/* Adjust sidebar group actions for RTL */
html[dir="rtl"] [data-sidebar="group-action"] {
  right: auto;
  left: 0.75rem;
}

/* Flip margins and paddings */
html[dir="rtl"] .ml-auto {
  margin-left: 0 !important;
  margin-right: auto !important;
}

html[dir="rtl"] .mr-auto {
  margin-right: 0 !important;
  margin-left: auto !important;
}

/* Flip flexbox directions */
html[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

/* Flip margins for spacing utilities */
html[dir="rtl"] .ml-1, html[dir="rtl"] .ml-2, html[dir="rtl"] .ml-3, html[dir="rtl"] .ml-4 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

html[dir="rtl"] .mr-1, html[dir="rtl"] .mr-2, html[dir="rtl"] .mr-3, html[dir="rtl"] .mr-4 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

/* Flip padding for spacing utilities */
html[dir="rtl"] .pl-1, html[dir="rtl"] .pl-2, html[dir="rtl"] .pl-3, html[dir="rtl"] .pl-4 {
  padding-left: 0 !important;
  padding-right: 0.25rem !important;
}

html[dir="rtl"] .pr-1, html[dir="rtl"] .pr-2, html[dir="rtl"] .pr-3, html[dir="rtl"] .pr-4 {
  padding-right: 0 !important;
  padding-left: 0.25rem !important;
}

/* Flip icons */
html[dir="rtl"] .transform-flip {
  transform: scaleX(-1);
}

/* Adjust text alignment in form elements */
html[dir="rtl"] input,
html[dir="rtl"] textarea,
html[dir="rtl"] select {
  text-align: right;
}

/* Adjust button icons */
html[dir="rtl"] .button-icon-left {
  margin-right: 0;
  margin-left: 0.5rem;
}

html[dir="rtl"] .button-icon-right {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* Adjust dropdown menus */
html[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}

/* Adjust navigation */
html[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

html[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Adjust grid columns */
html[dir="rtl"] .grid-cols-1 > * {
  grid-column: auto;
}

/* Adjust border radius for RTL */
html[dir="rtl"] .rounded-l {
  border-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

html[dir="rtl"] .rounded-r {
  border-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* Adjust positioning */
html[dir="rtl"] .left-0 {
  left: auto;
  right: 0;
}

html[dir="rtl"] .right-0 {
  right: auto;
}

/* Adjust text alignment classes */
html[dir="rtl"] .text-left {
  text-align: right;
}

html[dir="rtl"] .text-right {
  text-align: left;
}

/* Adjust flexbox alignment */
html[dir="rtl"] .items-start {
  align-items: flex-end;
}

html[dir="rtl"] .items-end {
  align-items: flex-start;
}

/* Adjust justify content */
html[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

html[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

/* Adjust chevron icons */
html[dir="rtl"] .chevron-right {
  transform: rotate(180deg);
}

html[dir="rtl"] .chevron-left {
  transform: rotate(180deg);
}

/* Dashboard header adjustments for RTL */
html[dir="rtl"] .header-fixed {
  flex-direction: row-reverse;
}

/* Adjust dashboard content spacing for RTL */
html[dir="rtl"] .peer-header-fixed header {
  margin-right: 16rem;
  margin-left: 0;
}

/* Adjust dashboard main content for RTL */
html[dir="rtl"] main {
  direction: rtl;
}

/* Adjust dashboard cards for RTL */
html[dir="rtl"] .card {
  direction: rtl;
}

/* Adjust dashboard buttons for RTL */
html[dir="rtl"] .button {
  direction: rtl;
}

/* Adjust dashboard tabs for RTL */
html[dir="rtl"] .tabs-list {
  flex-direction: row-reverse;
}

/* Adjust dashboard search for RTL */
html[dir="rtl"] .search-input {
  direction: rtl;
  text-align: right;
}

/* Adjust dashboard dropdown menus for RTL */
html[dir="rtl"] .dropdown-content {
  direction: rtl;
  text-align: right;
}
