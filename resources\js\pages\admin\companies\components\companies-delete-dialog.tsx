'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { Company } from '../data/schema'
import axios from 'axios'
import { router } from '@inertiajs/react'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: Company
}

export function CompaniesDeleteDialog({ open, onOpenChange, currentRow }: Props) {
  const [deleteInput, setDeleteInput] = useState('')
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    if (deleteInput !== currentRow.name) {
      toast({
        title: 'Erreur',
        description: 'Le nom saisi ne correspond pas',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    try {
      await axios.delete(`/api/companies/${currentRow.id}`)
      toast({
        title: 'Succès',
        description: 'Entreprise supprimée avec succès',
      })
      onOpenChange(false)
      setDeleteInput('')
      router.reload()
    } catch (error: any) {
      console.error('Error deleting company:', error)
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={(state) => {
        if (!loading) {
          setDeleteInput('')
          onOpenChange(state)
        }
      }}
      title="Supprimer l'entreprise"
      desc="Cette action est irréversible. L'entreprise sera définitivement supprimée."
      confirmText={loading ? "Suppression..." : "Supprimer"}
      handleConfirm={handleDelete}
      disabled={deleteInput !== currentRow.name || loading}
      destructive={true}
    >
      <Alert variant="destructive">
        <IconAlertTriangle className="h-4 w-4" />
        <AlertTitle>Attention !</AlertTitle>
        <AlertDescription>
          Cette action supprimera définitivement l&apos;entreprise &quot;{currentRow.name}&quot;.
        </AlertDescription>
      </Alert>
      
      <div className="space-y-2">
        <Label htmlFor="delete-input">
          Pour confirmer, tapez le nom de l&apos;entreprise : <strong>{currentRow.name}</strong>
        </Label>
        <Input
          id="delete-input"
          value={deleteInput}
          onChange={(e) => setDeleteInput(e.target.value)}
          placeholder="Tapez le nom de l'entreprise"
          disabled={loading}
        />
      </div>
    </ConfirmDialog>
  )
}
