import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { PositionType } from '../data/schema'

type PositionTypesDialogType = 'add' | 'edit' | 'delete'

interface PositionTypesContextType {
  open: PositionTypesDialogType | null
  setOpen: (str: PositionTypesDialogType | null) => void
  currentRow: PositionType | null
  setCurrentRow: React.Dispatch<React.SetStateAction<PositionType | null>>
}

const PositionTypesContext = React.createContext<PositionTypesContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function PositionTypesProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<PositionTypesDialogType>(null)
  const [currentRow, setCurrentRow] = useState<PositionType | null>(null)

  return (
    <PositionTypesContext.Provider value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </PositionTypesContext.Provider>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const usePositionTypes = () => {
  const positionTypesContext = React.useContext(PositionTypesContext)

  if (!positionTypesContext) {
    throw new Error('usePositionTypes has to be used within <PositionTypesContext>')
  }

  return positionTypesContext
}
