import { IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useCompanies } from '../context/companies-context'

export function CompaniesPrimaryButtons() {
  const { setOpen } = useCompanies()

  return (
    <div className='flex items-center space-x-2'>
      <Button
        size='sm'
        onClick={() => setOpen('add')}
        className='h-8'
      >
        <IconPlus className='mr-2 h-4 w-4' />
        Ajouter une entreprise
      </Button>
    </div>
  )
}
