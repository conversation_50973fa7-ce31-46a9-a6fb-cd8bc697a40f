import { router, usePage } from "@inertiajs/react"
import {
  unstable_AdapterOptions as AdapterOptions,
  unstable_createAdapterProvider as createAdapterProvider,
  renderQueryString,
} from "nuqs/adapters/custom"

function useNuqsInertiaAdapter() {
  let pageUrl: string;

  try {
    // Try to get the URL from Inertia's usePage hook
    pageUrl = usePage().url;
  } catch (error) {
    // Fallback to window.location if usePage is not available
    console.warn('usePage not available in NuqsAdapter, falling back to window.location');
    pageUrl = window.location.pathname + window.location.search;
  }

  const { searchParams } = new URL(window.location.origin + pageUrl)

  const updateUrl = (search: URLSearchParams, options: AdapterOptions) => {
    const url = new URL(window.location.href)
    url.search = renderQueryString(search)

    router.visit(`${window.location.pathname}${renderQueryString(search)}`, {
      replace: options.history === "replace",
      preserveScroll: !options.scroll,
      preserveState: true,
    })
  }

  return {
    searchParams,
    updateUrl,
  }
}

export const NuqsAdapter = createAdapterProvider(useNuqsInertiaAdapter)
