<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Seed position_types
        DB::table('position_types')->insert([
            ['code' => 'CDI', 'name' => 'CDI'],
            ['code' => 'CDD', 'name' => 'CDD'],
            ['code' => 'STAGE', 'name' => 'Stage'],
            ['code' => 'FREELANCE', 'name' => 'Freelance'],
            ['code' => 'ALTERNANCE', 'name' => 'Alternance'],
        ]);

        // Seed companies
        DB::table('companies')->insert([
            ['name' => 'Acme Inc.', 'introduction' => null],
            ['name' => 'Tech Solutions', 'introduction' => null],
            ['name' => 'Design Studio', 'introduction' => null],
            ['name' => 'Web Agency', 'introduction' => null],
            ['name' => 'StartUp Co.', 'introduction' => null],
        ]);
    }

    public function down(): void
    {
        DB::table('position_types')->whereIn('code', ['CDI', 'CDD', 'STAGE', 'FREELANCE', 'ALTERNANCE'])->delete();
        DB::table('companies')->whereIn('name', [
            'Acme Inc.',
            'Tech Solutions',
            'Design Studio',
            'Web Agency',
            'StartUp Co.'
        ])->delete();
    }
}; 