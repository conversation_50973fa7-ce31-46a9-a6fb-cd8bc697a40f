import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Get the initial language from localStorage or use browser detection
const getInitialLanguage = () => {
  if (typeof window !== 'undefined') {
    const storedLocale = localStorage.getItem('locale');
    if (storedLocale) {
      return storedLocale;
    }
  }
  return navigator.language.split('-')[0] || 'en';
};

// Define resources
const resources = {
  en: {
    translation: {
      // Auth translations
      "Login": "Login",
      "Register": "Register",
      "Email": "Email",
      "Password": "Password",
      "Remember me": "Remember me",
      "Forgot your password?": "Forgot your password?",
      "Log in": "Log in",
      "Language": "Language",
      "Don't have an account?": "Don't have an account?",
      "Sign up": "Sign up",
      "Enter your email and password below to login to your account.": "Enter your email and password below to login to your account.",
      "By continuing, you agree to our": "By continuing, you agree to our",
      "Terms of Service": "Terms of Service",
      "and": "and",
      "Privacy Policy": "Privacy Policy",
      "This platform has saved me precious time and allowed me to deliver exceptional designs to my clients faster than ever.": "This platform has saved me precious time and allowed me to deliver exceptional designs to my clients faster than ever.",
      "Afaq Administration": "Afaq Administration",
      "Or continue with": "Or continue with",
      "Sign in with Google": "Sign in with Google",
      "Sign in with LinkedIn": "Sign in with LinkedIn",
      "Sign in with Facebook": "Sign in with Facebook",
      "Logging in...": "Logging in...",

      // Dashboard translations
      "Dashboard": "Dashboard",
      "Download": "Download",
      "Overview": "Overview",
      "Analytics": "Analytics",
      "Reports": "Reports",
      "Notifications": "Notifications",
      "Total Revenue": "Total Revenue",
      "from last month": "from last month",
      "Subscriptions": "Subscriptions",
      "Sales": "Sales",
      "Active Now": "Active Now",
      "since last hour": "since last hour",
      "Recent Sales": "Recent Sales",
      "You made 265 sales this month.": "You made 265 sales this month.",

      // Sidebar translations
      "General": "General",
      "Résumé de profil": "Profile Summary",
      "Chercher des offres": "Search Jobs",
      "Préférences": "Preferences",
      "Administration": "Administration",
      "Gestion des emplois": "Job Management",
      "Offres d'emploi": "Job Offers",
      "Entreprises": "Companies",
      "Types de postes": "Position Types",
      "Template pages": "Template pages",
      "Tasks": "Tasks",
      "Apps": "Apps",
      "Chats": "Chats",
      "Ai Chats": "AI Chats",
      "Charts": "Charts",
      "Users": "Users",
      "Ecommerce": "Ecommerce",
      "Orders": "Orders",
      "Products": "Products",
      "Product": "Product",
      "Pages": "Pages",
      "Auth": "Authentication",
      "Sign In": "Sign In",
      "Sign In (2 Col)": "Sign In (2 Col)",
      "Sign Up": "Sign Up",
      "Forgot Password": "Forgot Password",
      "OTP": "OTP",
      "Errors": "Errors",
      "Unauthorized": "Unauthorized",
      "Forbidden": "Forbidden",
      "Not Found": "Not Found",
      "Internal Server Error": "Internal Server Error",
      "Maintenance Error": "Maintenance Error",
      "Other": "Other",
      "Settings": "Settings",
      "Account": "Account",
      "Appearance": "Appearance",
      "Display": "Display",
      "Help Center": "Help Center",
      "Log out": "Log out",

      // Job Search translations
      "Search Jobs": "Search Jobs",
      "Search...": "Search...",
      "Example": "Example",
      "designer or #paris": "designer or #paris",
      "Job Type": "Job Type",
      "Location": "Location",
      "Company": "Company",
      "Share": "Share",
      "Apply": "Apply",
      "Job Description": "Job Description",
      "Prerequisites": "Prerequisites",
      "Responsibilities": "Responsibilities",
      "Apply Now": "Apply Now",
      "Search jobs...": "Search jobs...",
      "jobs found": "jobs found",
      "Sort by": "Sort by",
      "Date": "Date",
      "Loading...": "Loading...",
      "No jobs found": "No jobs found",
      "Previous": "Previous",
      "Page": "Page",
      "Next": "Next",
      "Select a job to see details": "Select a job to see details",

      // Landing page translations
      // Header
      "Features": "Features",
      "How it works": "How it works",
      "Testimonials": "Testimonials",
      "Pricing": "Pricing",
      "User": "User",

      // Hero section
      "Find your international job with Afeq": "Find your international job with Afeq",
      "The platform that automates your job search and helps you secure international opportunities": "The platform that automates your job search and helps you secure international opportunities",
      "Creation of professional CVs adapted to international standards": "Creation of professional CVs adapted to international standards",
      "Automated search for job offers matching your profile": "Automated search for job offers matching your profile",
      "Automatic application and response tracking in one place": "Automatic application and response tracking in one place",
      "Get started for free": "Get started for free",

      // Features section
      "How Afeq helps you find a job": "How Afeq helps you find a job",
      "Our platform automates and simplifies every step of your international job search": "Our platform automates and simplifies every step of your international job search",
      "Profile and CV creation": "Profile and CV creation",
      "Create a professional profile and generate a CV adapted to international standards": "Create a professional profile and generate a CV adapted to international standards",
      "Personalized job search": "Personalized job search",
      "Our algorithm finds job offers that perfectly match your profile": "Our algorithm finds job offers that perfectly match your profile",
      "Automatic application": "Automatic application",
      "Apply automatically to selected offers and track your applications": "Apply automatically to selected offers and track your applications",

      // How it works section
      "A simple 4-step process to find your international job": "A simple 4-step process to find your international job",
      "Create your account": "Create your account",
      "Sign up and complete your profile with your skills and experiences": "Sign up and complete your profile with your skills and experiences",
      "Generate your CV": "Generate your CV",
      "Our assistant helps you create a professional CV adapted to international standards": "Our assistant helps you create a professional CV adapted to international standards",
      "Find job offers": "Find job offers",
      "Our algorithm searches for offers that match your profile": "Our algorithm searches for offers that match your profile",
      "Apply and track": "Apply and track",
      "Apply automatically and track your applications directly on the platform": "Apply automatically and track your applications directly on the platform",

      // Testimonials section
      "What our users say": "What our users say",
      "Discover how Afeq has helped young people find international jobs": "Discover how Afeq has helped young people find international jobs",
      "Thanks to Afeq, I found a job in France in just 3 weeks. The process was simple and efficient.": "Thanks to Afeq, I found a job in France in just 3 weeks. The process was simple and efficient.",
      "Web Developer": "Web Developer",
      "Paris, France": "Paris, France",
      "The automatic CV generation saved me a lot of time, and the interview tips were very helpful.": "The automatic CV generation saved me a lot of time, and the interview tips were very helpful.",
      "Software Engineer": "Software Engineer",
      "Montreal, Canada": "Montreal, Canada",
      "I received several job offers perfectly matching my profile. Afeq really understood my skills.": "I received several job offers perfectly matching my profile. Afeq really understood my skills.",
      "Cloud Architect": "Cloud Architect",
      "Berlin, Germany": "Berlin, Germany",

      // CTA section
      "Ready to start your international career?": "Ready to start your international career?",
      "Join thousands of young people who have already found their job through Afeq": "Join thousands of young people who have already found their job through Afeq",
      "Create an account": "Create an account",

      // Footer
      "Afeq helps young people find international job opportunities through an innovative and automated platform.": "Afeq helps young people find international job opportunities through an innovative and automated platform.",
      "Quick links": "Quick links",
      "Home": "Home",
      "Legal": "Legal",
      "Terms of Use": "Terms of Use",
      "Cookie Policy": "Cookie Policy",
      "Contact": "Contact",
      "All rights reserved.": "All rights reserved."
    }
  },
  fr: {
    translation: {
      // Auth translations
      "Login": "Connexion",
      "Register": "S'inscrire",
      "Email": "Email",
      "Password": "Mot de passe",
      "Remember me": "Se souvenir de moi",
      "Forgot your password?": "Mot de passe oublié ?",
      "Log in": "Se connecter",
      "Language": "Langue",
      "Don't have an account?": "Pas encore de compte ?",
      "Sign up": "S'inscrire",
      "Enter your email and password below to login to your account.": "Entrez votre email et mot de passe ci-dessous pour vous connecter à votre compte.",
      "By continuing, you agree to our": "En continuant, vous acceptez nos",
      "Terms of Service": "Conditions d'utilisation",
      "and": "et",
      "Privacy Policy": "Politique de confidentialité",
      "This platform has saved me precious time and allowed me to deliver exceptional designs to my clients faster than ever.": "Cette plateforme m'a fait gagner un temps précieux et m'a permis de livrer des designs exceptionnels à mes clients plus rapidement que jamais.",
      "Afaq Administration": "Administration Afaq",
      "Or continue with": "Ou continuer avec",
      "Sign in with Google": "Se connecter avec Google",
      "Sign in with LinkedIn": "Se connecter avec LinkedIn",
      "Sign in with Facebook": "Se connecter avec Facebook",
      "Logging in...": "Connexion en cours...",

      // Dashboard translations
      "Dashboard": "Tableau de bord",
      "Download": "Télécharger",
      "Overview": "Aperçu",
      "Analytics": "Analytique",
      "Reports": "Rapports",
      "Notifications": "Notifications",
      "Total Revenue": "Revenu total",
      "from last month": "par rapport au mois dernier",
      "Subscriptions": "Abonnements",
      "Sales": "Ventes",
      "Active Now": "Actifs maintenant",
      "since last hour": "depuis la dernière heure",
      "Recent Sales": "Ventes récentes",
      "You made 265 sales this month.": "Vous avez réalisé 265 ventes ce mois-ci.",

      // Sidebar translations
      "General": "Général",
      "Résumé de profil": "Résumé de profil",
      "Chercher des offres": "Chercher des offres",
      "Préférences": "Préférences",
      "Administration": "Administration",
      "Gestion des emplois": "Gestion des emplois",
      "Offres d'emploi": "Offres d'emploi",
      "Entreprises": "Entreprises",
      "Types de postes": "Types de postes",
      "Template pages": "Pages de modèles",
      "Tasks": "Tâches",
      "Apps": "Applications",
      "Chats": "Discussions",
      "Ai Chats": "Discussions IA",
      "Charts": "Graphiques",
      "Users": "Utilisateurs",
      "Ecommerce": "Commerce électronique",
      "Orders": "Commandes",
      "Products": "Produits",
      "Product": "Produit",
      "Pages": "Pages",
      "Auth": "Authentification",
      "Sign In": "Se connecter",
      "Sign In (2 Col)": "Se connecter (2 Col)",
      "Sign Up": "S'inscrire",
      "Forgot Password": "Mot de passe oublié",
      "OTP": "Code OTP",
      "Errors": "Erreurs",
      "Unauthorized": "Non autorisé",
      "Forbidden": "Interdit",
      "Not Found": "Non trouvé",
      "Internal Server Error": "Erreur serveur interne",
      "Maintenance Error": "Erreur de maintenance",
      "Other": "Autre",
      "Settings": "Paramètres",
      "Account": "Compte",
      "Appearance": "Apparence",
      "Display": "Affichage",
      "Help Center": "Centre d'aide",
      "Log out": "Se déconnecter",

      // Job Search translations
      "Search Jobs": "Chercher des offres",
      "Search...": "Rechercher...",
      "Example": "Exemple",
      "designer or #paris": "designer ou #paris",
      "Job Type": "Type",
      "Location": "Localité",
      "Company": "Entreprise",
      "Share": "Partager",
      "Apply": "Postuler",
      "Job Description": "Description du poste",
      "Prerequisites": "Prérequis",
      "Responsibilities": "Responsabilités",
      "Apply Now": "Postuler maintenant",
      "Search jobs...": "Rechercher des offres...",
      "jobs found": "offres trouvées",
      "Sort by": "Trier par",
      "Date": "Date",
      "Loading...": "Chargement...",
      "No jobs found": "Aucune offre trouvée",
      "Previous": "Précédent",
      "Page": "Page",
      "Next": "Suivant",
      "Select a job to see details": "Sélectionnez une offre pour voir les détails",

      // Landing page translations
      // Header
      "Features": "Fonctionnalités",
      "How it works": "Comment ça marche",
      "Testimonials": "Témoignages",
      "Pricing": "Tarifs",
      "User": "Utilisateur",

      // Hero section
      "Find your international job with Afeq": "Trouvez votre emploi à l'international avec Afeq",
      "The platform that automates your job search and helps you secure international opportunities": "La plateforme qui automatise votre recherche d'emploi et vous aide à décrocher des opportunités à l'international",
      "Creation of professional CVs adapted to international standards": "Création de CV professionnels adaptés aux standards internationaux",
      "Automated search for job offers matching your profile": "Recherche automatisée d'offres d'emploi correspondant à votre profil",
      "Automatic application and response tracking in one place": "Candidature automatique et suivi des réponses en un seul endroit",
      "Get started for free": "Commencer gratuitement",

      // Features section
      "How Afeq helps you find a job": "Comment Afeq vous aide à trouver un emploi",
      "Our platform automates and simplifies every step of your international job search": "Notre plateforme automatise et simplifie chaque étape de votre recherche d'emploi à l'international",
      "Profile and CV creation": "Création de profil et CV",
      "Create a professional profile and generate a CV adapted to international standards": "Créez un profil professionnel et générez un CV adapté aux standards internationaux",
      "Personalized job search": "Recherche d'offres personnalisée",
      "Our algorithm finds job offers that perfectly match your profile": "Notre algorithme trouve les offres qui correspondent parfaitement à votre profil",
      "Automatic application": "Candidature automatique",
      "Apply automatically to selected offers and track your applications": "Postulez automatiquement aux offres sélectionnées et suivez vos candidatures",

      // How it works section
      "A simple 4-step process to find your international job": "Un processus simple en 4 étapes pour trouver votre emploi à l'international",
      "Create your account": "Créez votre compte",
      "Sign up and complete your profile with your skills and experiences": "Inscrivez-vous et complétez votre profil avec vos compétences et expériences",
      "Generate your CV": "Générez votre CV",
      "Our assistant helps you create a professional CV adapted to international standards": "Notre assistant vous aide à créer un CV professionnel adapté aux standards internationaux",
      "Find job offers": "Trouvez des offres",
      "Our algorithm searches for offers that match your profile": "Notre algorithme recherche les offres qui correspondent à votre profil",
      "Apply and track": "Postulez et suivez",
      "Apply automatically and track your applications directly on the platform": "Postulez automatiquement et suivez vos candidatures directement sur la plateforme",

      // Testimonials section
      "What our users say": "Ce que disent nos utilisateurs",
      "Discover how Afeq has helped young people find international jobs": "Découvrez comment Afeq a aidé des jeunes à trouver leur emploi à l'international",
      "Thanks to Afeq, I found a job in France in just 3 weeks. The process was simple and efficient.": "Grâce à Afeq, j'ai trouvé un emploi en France en seulement 3 semaines. Le processus était simple et efficace.",
      "Web Developer": "Développeuse Web",
      "Paris, France": "Paris, France",
      "The automatic CV generation saved me a lot of time, and the interview tips were very helpful.": "La génération automatique de CV m'a fait gagner beaucoup de temps, et les conseils pour l'entretien étaient très utiles.",
      "Software Engineer": "Ingénieur Logiciel",
      "Montreal, Canada": "Montréal, Canada",
      "I received several job offers perfectly matching my profile. Afeq really understood my skills.": "J'ai reçu plusieurs offres d'emploi correspondant parfaitement à mon profil. Afeq a vraiment compris mes compétences.",
      "Cloud Architect": "Architecte Cloud",
      "Berlin, Germany": "Berlin, Allemagne",

      // CTA section
      "Ready to start your international career?": "Prêt à commencer votre carrière à l'international?",
      "Join thousands of young people who have already found their job through Afeq": "Rejoignez des milliers de jeunes qui ont déjà trouvé leur emploi grâce à Afeq",
      "Create an account": "Créer un compte",

      // Footer
      "Afeq helps young people find international job opportunities through an innovative and automated platform.": "Afeq aide les jeunes à trouver des opportunités d'emploi à l'international grâce à une plateforme innovante et automatisée.",
      "Quick links": "Liens rapides",
      "Home": "Accueil",
      "Legal": "Légal",
      "Terms of Use": "Conditions d'utilisation",
      "Cookie Policy": "Politique de cookies",
      "Contact": "Contact",
      "All rights reserved.": "Tous droits réservés."
    }
  },
  ar: {
    translation: {
      // Auth translations
      "Login": "تسجيل الدخول",
      "Register": "تسجيل",
      "Email": "البريد الإلكتروني",
      "Password": "كلمة المرور",
      "Remember me": "تذكرني",
      "Forgot your password?": "نسيت كلمة المرور؟",
      "Log in": "تسجيل الدخول",
      "Language": "اللغة",
      "Don't have an account?": "ليس لديك حساب؟",
      "Sign up": "إنشاء حساب",
      "Enter your email and password below to login to your account.": "أدخل بريدك الإلكتروني وكلمة المرور أدناه لتسجيل الدخول إلى حسابك.",
      "By continuing, you agree to our": "بالمتابعة، فإنك توافق على",
      "Terms of Service": "شروط الخدمة",
      "and": "و",
      "Privacy Policy": "سياسة الخصوصية",
      "This platform has saved me precious time and allowed me to deliver exceptional designs to my clients faster than ever.": "وفرت لي هذه المنصة وقتًا ثمينًا وسمحت لي بتقديم تصاميم استثنائية لعملائي بشكل أسرع من أي وقت مضى.",
      "Afaq Administration": "إدارة آفاق",
      "Or continue with": "أو تابع باستخدام",
      "Sign in with Google": "تسجيل الدخول بواسطة جوجل",
      "Sign in with LinkedIn": "تسجيل الدخول بواسطة لينكد إن",
      "Sign in with Facebook": "تسجيل الدخول بواسطة فيسبوك",
      "Logging in...": "جاري تسجيل الدخول...",

      // Dashboard translations
      "Dashboard": "لوحة التحكم",
      "Download": "تحميل",
      "Overview": "نظرة عامة",
      "Analytics": "التحليلات",
      "Reports": "التقارير",
      "Notifications": "الإشعارات",
      "Total Revenue": "إجمالي الإيرادات",
      "from last month": "من الشهر الماضي",
      "Subscriptions": "الاشتراكات",
      "Sales": "المبيعات",
      "Active Now": "نشط الآن",
      "since last hour": "منذ الساعة الماضية",
      "Recent Sales": "المبيعات الأخيرة",
      "You made 265 sales this month.": "لقد قمت بـ 265 عملية بيع هذا الشهر.",

      // Sidebar translations
      "General": "عام",
      "Résumé de profil": "ملخص الملف الشخصي",
      "Chercher des offres": "البحث عن الوظائف",
      "Préférences": "التفضيلات",
      "Administration": "الإدارة",
      "Gestion des emplois": "إدارة الوظائف",
      "Offres d'emploi": "عروض العمل",
      "Entreprises": "الشركات",
      "Types de postes": "أنواع المناصب",
      "Template pages": "صفحات القوالب",
      "Tasks": "المهام",
      "Apps": "التطبيقات",
      "Chats": "المحادثات",
      "Ai Chats": "محادثات الذكاء الاصطناعي",
      "Charts": "الرسوم البيانية",
      "Users": "المستخدمون",
      "Ecommerce": "التجارة الإلكترونية",
      "Orders": "الطلبات",
      "Products": "المنتجات",
      "Product": "المنتج",
      "Pages": "الصفحات",
      "Auth": "المصادقة",
      "Sign In": "تسجيل الدخول",
      "Sign In (2 Col)": "تسجيل الدخول (عمودين)",
      "Sign Up": "إنشاء حساب",
      "Forgot Password": "نسيت كلمة المرور",
      "OTP": "رمز التحقق",
      "Errors": "الأخطاء",
      "Unauthorized": "غير مخول",
      "Forbidden": "محظور",
      "Not Found": "غير موجود",
      "Internal Server Error": "خطأ خادم داخلي",
      "Maintenance Error": "خطأ صيانة",
      "Other": "أخرى",
      "Settings": "الإعدادات",
      "Account": "الحساب",
      "Appearance": "المظهر",
      "Display": "العرض",
      "Help Center": "مركز المساعدة",
      "Log out": "تسجيل الخروج",

      // Job Search translations
      "Search Jobs": "البحث عن الوظائف",
      "Search...": "بحث...",
      "Example": "مثال",
      "designer or #paris": "مصمم أو #باريس",
      "Job Type": "نوع الوظيفة",
      "Location": "الموقع",
      "Company": "الشركة",
      "Share": "مشاركة",
      "Apply": "تقدم",
      "Job Description": "وصف الوظيفة",
      "Prerequisites": "المتطلبات المسبقة",
      "Responsibilities": "المسؤوليات",
      "Apply Now": "تقدم الآن",
      "Search jobs...": "البحث عن الوظائف...",
      "jobs found": "وظيفة موجودة",
      "Sort by": "ترتيب حسب",
      "Date": "التاريخ",
      "Loading...": "جاري التحميل...",
      "No jobs found": "لم يتم العثور على وظائف",
      "Previous": "السابق",
      "Page": "صفحة",
      "Next": "التالي",
      "Select a job to see details": "اختر وظيفة لرؤية التفاصيل",

      // Landing page translations
      // Header
      "Features": "الميزات",
      "How it works": "كيف يعمل",
      "Testimonials": "الشهادات",
      "Pricing": "الأسعار",
      "User": "مستخدم",

      // Hero section
      "Find your international job with Afeq": "ابحث عن وظيفتك الدولية مع آفاق",
      "The platform that automates your job search and helps you secure international opportunities": "المنصة التي تؤتمت بحثك عن وظيفة وتساعدك في تأمين فرص دولية",
      "Creation of professional CVs adapted to international standards": "إنشاء سير ذاتية احترافية متوافقة مع المعايير الدولية",
      "Automated search for job offers matching your profile": "بحث آلي عن عروض العمل المطابقة لملفك الشخصي",
      "Automatic application and response tracking in one place": "تقديم طلبات العمل وتتبع الردود تلقائيًا في مكان واحد",
      "Get started for free": "ابدأ مجانًا",

      // Features section
      "How Afeq helps you find a job": "كيف يساعدك آفاق في العثور على وظيفة",
      "Our platform automates and simplifies every step of your international job search": "منصتنا تؤتمت وتبسط كل خطوة من بحثك عن وظيفة دولية",
      "Profile and CV creation": "إنشاء الملف الشخصي والسيرة الذاتية",
      "Create a professional profile and generate a CV adapted to international standards": "أنشئ ملفًا شخصيًا احترافيًا وقم بإنشاء سيرة ذاتية متوافقة مع المعايير الدولية",
      "Personalized job search": "بحث مخصص عن الوظائف",
      "Our algorithm finds job offers that perfectly match your profile": "خوارزميتنا تجد عروض العمل التي تتطابق تمامًا مع ملفك الشخصي",
      "Automatic application": "تقديم طلبات العمل تلقائيًا",
      "Apply automatically to selected offers and track your applications": "قدم طلبات العمل تلقائيًا للعروض المختارة وتتبع طلباتك",

      // How it works section
      "A simple 4-step process to find your international job": "عملية بسيطة من 4 خطوات للعثور على وظيفتك الدولية",
      "Create your account": "أنشئ حسابك",
      "Sign up and complete your profile with your skills and experiences": "سجل وأكمل ملفك الشخصي بمهاراتك وخبراتك",
      "Generate your CV": "أنشئ سيرتك الذاتية",
      "Our assistant helps you create a professional CV adapted to international standards": "مساعدنا يساعدك على إنشاء سيرة ذاتية احترافية متوافقة مع المعايير الدولية",
      "Find job offers": "ابحث عن عروض العمل",
      "Our algorithm searches for offers that match your profile": "خوارزميتنا تبحث عن العروض التي تتطابق مع ملفك الشخصي",
      "Apply and track": "قدم وتتبع",
      "Apply automatically and track your applications directly on the platform": "قدم طلبات العمل تلقائيًا وتتبع طلباتك مباشرة على المنصة",

      // Testimonials section
      "What our users say": "ماذا يقول مستخدمونا",
      "Discover how Afeq has helped young people find international jobs": "اكتشف كيف ساعد آفاق الشباب في العثور على وظائف دولية",
      "Thanks to Afeq, I found a job in France in just 3 weeks. The process was simple and efficient.": "بفضل آفاق، وجدت وظيفة في فرنسا في 3 أسابيع فقط. كانت العملية بسيطة وفعالة.",
      "Web Developer": "مطور ويب",
      "Paris, France": "باريس، فرنسا",
      "The automatic CV generation saved me a lot of time, and the interview tips were very helpful.": "وفر لي إنشاء السيرة الذاتية التلقائي الكثير من الوقت، وكانت نصائح المقابلة مفيدة جدًا.",
      "Software Engineer": "مهندس برمجيات",
      "Montreal, Canada": "مونتريال، كندا",
      "I received several job offers perfectly matching my profile. Afeq really understood my skills.": "تلقيت العديد من عروض العمل التي تتطابق تمامًا مع ملفي الشخصي. آفاق فهم مهاراتي حقًا.",
      "Cloud Architect": "مهندس سحابي",
      "Berlin, Germany": "برلين، ألمانيا",

      // CTA section
      "Ready to start your international career?": "هل أنت مستعد لبدء حياتك المهنية الدولية؟",
      "Join thousands of young people who have already found their job through Afeq": "انضم إلى آلاف الشباب الذين وجدوا وظائفهم بالفعل من خلال آفاق",
      "Create an account": "إنشاء حساب",

      // Footer
      "Afeq helps young people find international job opportunities through an innovative and automated platform.": "يساعد آفاق الشباب في العثور على فرص عمل دولية من خلال منصة مبتكرة وآلية.",
      "Quick links": "روابط سريعة",
      "Home": "الرئيسية",
      "Legal": "قانوني",
      "Terms of Use": "شروط الاستخدام",
      "Cookie Policy": "سياسة ملفات تعريف الارتباط",
      "Contact": "اتصل بنا",
      "All rights reserved.": "جميع الحقوق محفوظة."
    }
  }
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    lng: getInitialLanguage(),
    debug: true,
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    react: {
      useSuspense: false,
    },
    detection: {
      // Order of detection
      order: ['path', 'localStorage', 'navigator'],

      // Look for language in the URL path
      lookupFromPathIndex: 0,

      // Convert path language to i18n language format
      convertPathToLanguage: (lng) => {
        console.log('Converting path language:', lng);
        return lng;
      }
    }
  });

console.log('i18n initialized with language:', i18n.language);

// Function to load translations from Laravel
export const loadTranslations = (locale: string, translations: Record<string, string>) => {
  console.log('Loading translations for locale:', locale);
  console.log('Translations count:', Object.keys(translations).length);

  // Merge with existing translations
  const existingResources = i18n.getResourceBundle(locale, 'translation') || {};
  const mergedTranslations = { ...existingResources, ...translations };

  console.log('Merged translations count:', Object.keys(mergedTranslations).length);

  // Replace the entire resource bundle to ensure all translations are updated
  i18n.addResourceBundle(locale, 'translation', mergedTranslations, true, true);

  // Force language reload if it's the current language
  if (i18n.language === locale) {
    i18n.reloadResources([locale], ['translation']);
  }
};

// Function to update translations
export const updateTranslations = (locale: string, translations: Record<string, string>) => {
  try {
    if (locale && translations) {
      // Add resources only if they don't exist yet
      if (!i18n.hasResourceBundle(locale, 'translation')) {
        i18n.addResourceBundle(locale, 'translation', translations);
      } else {
        // Update existing resources
        Object.keys(translations).forEach(key => {
          i18n.addResource(locale, 'translation', key, translations[key]);
        });
      }
    }
  } catch (error) {
    console.error('Error updating translations:', error);
  }
};

// Function to change language
export const changeLanguage = (locale: string) => {
  try {
    if (locale) {
      i18n.changeLanguage(locale);
      localStorage.setItem('locale', locale);
    }
  } catch (error) {
    console.error('Error changing language:', error);
  }
};

export default i18n;
