import '../css/app.css';
import './bootstrap';
// Import i18n instance
import './lib/i18n';

import React, {StrictMode} from "react"
import {createInertiaApp} from '@inertiajs/react';
import {resolvePageComponent} from 'laravel-vite-plugin/inertia-helpers';
import {createRoot} from 'react-dom/client';
import {AppLayout} from "./layouts"
import {Providers, InertiaProviders} from "./providers"

const appName = import.meta.env.VITE_APP_NAME || 'Afeq';

createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: (name) =>
    resolvePageComponent(
      `./pages/${name}.tsx`,
      import.meta.glob('./pages/**/*.tsx'),
    ),
  setup({el, App, props}) {
    const root = createRoot(el);

    root.render(
      <StrictMode>
        <Providers>
          <InertiaProviders>
            <AppLayout>
              <App {...props} />
            </AppLayout>
          </InertiaProviders>
        </Providers>
      </StrictMode>
    )
  },
  progress: {
    color: '#4B5563',
  },
});
