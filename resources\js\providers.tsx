import * as React from "react"
import { useEffect } from "react"
import {QueryClientProvider} from "@tanstack/react-query"
import {ReactQueryDevtools} from "@tanstack/react-query-devtools"
import {HelmetProvider} from "react-helmet-async"
import {Toaster} from "@/components/ui/toaster"
import {TooltipProvider} from "@/components/ui/tooltip"
import {NuqsAdapter} from "@/lib/nuqs"
import {queryClient} from "@/lib/react-query"
import {ThemeProvider} from "@/context/theme-context"
import {SearchProvider} from "@/context/search-context"
import {LanguageProvider} from "@/context/language-context"

// Providers that don't depend on Inertia context
export function Providers({children}: any) {
  // Load translations directly from i18n
  useEffect(() => {
    console.log('Providers: Initializing i18n');
    // i18n is already initialized in i18n.ts
  }, []);

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <SearchProvider>
          <LanguageProvider initialLocale="en">
            <ThemeProvider
              defaultTheme="light"
              storageKey="app-ui-theme"
            >
              <TooltipProvider>{children}</TooltipProvider>

              <Toaster/>
            </ThemeProvider>
          </LanguageProvider>
        </SearchProvider>

        {/* Devtools */}
        <ReactQueryDevtools buttonPosition={"bottom-right"}/>
      </QueryClientProvider>
    </HelmetProvider>
  )
}

// Providers that depend on Inertia context
export function InertiaProviders({children}: any) {
  return (
    <NuqsAdapter>
      {children}
    </NuqsAdapter>
  )
}
