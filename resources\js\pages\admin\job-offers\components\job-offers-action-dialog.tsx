'use client'

import { useEffect, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ScrollArea } from '@/components/ui/scroll-area'
import { JobOffer, Company, PositionType } from '../data/schema'
import axios from 'axios'
import { router } from '@inertiajs/react'

const formSchema = z.object({
  title: z.string().min(1, 'Le titre est requis'),
  company_id: z.string().min(1, 'L\'entreprise est requise'),
  tags: z.string().optional(),
  description: z.string().min(1, 'La description est requise'),
  prerequisites: z.string().optional(),
  responsabilities: z.string().optional(),
  position_type_id: z.string().min(1, 'Le type de poste est requis'),
  city: z.string().min(1, 'La ville est requise'),
  country: z.string().min(1, 'Le pays est requis'),
  listedAt: z.string().min(1, 'La date de publication est requise'),
})

type JobOfferForm = z.infer<typeof formSchema>

interface Props {
  currentRow?: JobOffer
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function JobOffersActionDialog({ currentRow, open, onOpenChange }: Props) {
  const isEdit = !!currentRow
  const [companies, setCompanies] = useState<Company[]>([])
  const [positionTypes, setPositionTypes] = useState<PositionType[]>([])
  const [loading, setLoading] = useState(false)

  const form = useForm<JobOfferForm>({
    resolver: zodResolver(formSchema),
    defaultValues: isEdit
      ? {
          title: currentRow.title,
          company_id: currentRow.company_id.toString(),
          tags: currentRow.tags?.join(', ') || '',
          description: currentRow.description,
          prerequisites: currentRow.prerequisites || '',
          responsabilities: currentRow.responsabilities || '',
          position_type_id: currentRow.position_type_id.toString(),
          city: currentRow.city,
          country: currentRow.country,
          listedAt: currentRow.listedAt.toISOString().split('T')[0],
        }
      : {
          title: '',
          company_id: '',
          tags: '',
          description: '',
          prerequisites: '',
          responsabilities: '',
          position_type_id: '',
          city: '',
          country: 'France',
          listedAt: new Date().toISOString().split('T')[0],
        },
  })

  // Load companies and position types
  useEffect(() => {
    const loadData = async () => {
      try {
        const [companiesRes, positionTypesRes] = await Promise.all([
          axios.get('/api/companies'),
          axios.get('/api/position-types'),
        ])
        setCompanies(companiesRes.data)
        setPositionTypes(positionTypesRes.data)
      } catch (error) {
        console.error('Error loading data:', error)
        toast({
          title: 'Erreur',
          description: 'Impossible de charger les données',
          variant: 'destructive',
        })
      }
    }
    if (open) {
      loadData()
    }
  }, [open])

  const onSubmit = async (values: JobOfferForm) => {
    setLoading(true)
    try {
      const data = {
        ...values,
        company_id: parseInt(values.company_id),
        position_type_id: parseInt(values.position_type_id),
        tags: values.tags ? values.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
        listedAt: values.listedAt,
      }

      if (isEdit) {
        await axios.put(`/api/job-offers/${currentRow.id}`, data)
        toast({
          title: 'Succès',
          description: 'Offre d\'emploi modifiée avec succès',
        })
      } else {
        await axios.post('/api/job-offers', data)
        toast({
          title: 'Succès',
          description: 'Offre d\'emploi créée avec succès',
        })
      }

      form.reset()
      onOpenChange(false)
      router.reload()
    } catch (error: any) {
      console.error('Error saving job offer:', error)
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (!loading) {
          form.reset()
          onOpenChange(state)
        }
      }}
    >
      <DialogContent className='max-w-6xl w-[95vw] h-[90vh] flex flex-col p-0'>
        <DialogHeader className='text-left flex-shrink-0 p-6 pb-0'>
          <DialogTitle>{isEdit ? "Modifier l'offre" : "Ajouter une offre"}</DialogTitle>
          <DialogDescription>
            {isEdit ? "Modifiez les informations de l'offre d'emploi." : "Créez une nouvelle offre d'emploi."}
          </DialogDescription>
        </DialogHeader>
        <div className='flex-1 overflow-y-auto px-6'>
          <Form {...form}>
            <form
              id='job-offer-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='grid grid-cols-1 lg:grid-cols-2 gap-6 py-4'
            >
              {/* Left Column */}
              <div className='space-y-4'>
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Titre *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Développeur Full Stack'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='company_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Entreprise *</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder='Sélectionner une entreprise' />
                          </SelectTrigger>
                          <SelectContent>
                            {companies.map((company) => (
                              <SelectItem key={company.id} value={company.id.toString()}>
                                {company.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='position_type_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type de poste *</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder='Sélectionner un type' />
                          </SelectTrigger>
                          <SelectContent>
                            {positionTypes.map((type) => (
                              <SelectItem key={type.id} value={type.id.toString()}>
                                {type.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className='grid grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='city'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ville *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Paris'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='country'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Pays *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='France'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='listedAt'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date de publication *</FormLabel>
                      <FormControl>
                        <Input
                          type='date'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='tags'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Compétences</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='React, JavaScript, CSS (séparées par des virgules)'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Right Column */}
              <div className='space-y-4'>
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Description détaillée du poste...'
                          className='min-h-[120px] resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='prerequisites'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prérequis</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Prérequis et qualifications requises...'
                          className='min-h-[100px] resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='responsabilities'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Responsabilités</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Responsabilités et missions du poste...'
                          className='min-h-[100px] resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>
        <DialogFooter className='flex-shrink-0 p-6 pt-0'>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type='submit'
            form='job-offer-form'
            disabled={loading}
          >
            {loading ? 'Enregistrement...' : (isEdit ? 'Modifier' : 'Créer')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
