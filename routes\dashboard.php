<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Models\PositionType;
use App\Models\Company;
use App\Models\JobOffer;

Route::get('/', fn () => Inertia::render('dashboard/resume/index'))->name('dashboard');

Route::group(['prefix' => '/settings'], function () {
    Route::get('/', fn () => Inertia::render('settings/profile/index'))->name('dashboard.contacts.index');
    Route::get('/account', fn () => Inertia::render('settings/account/index'))->name('dashboard.contacts.accoubt');
    Route::get('/appearance', fn () => Inertia::render('settings/appearance/index'))->name('dashboard.file-manager.index');
    Route::get('/display', fn () => Inertia::render('settings/display/index'))->name('dashboard.notes.index');
    Route::get('/notifications', fn () => Inertia::render('settings/notifications/index'))->name('dashboard.scrumboard.index');
    Route::get('/profile', fn () => Inertia::render('settings/profile/index'))->name('dashboard.todo.index');
});

Route::get('/apps', fn () => Inertia::render('apps/index'))->name('dashboard.apps');
Route::get('/chats', fn () => Inertia::render('chats/index'))->name('dashboard.chats');
Route::get('/charts', fn () => Inertia::render('charts/index'))->name('dashboard.charts');
Route::get('/orders', fn () => Inertia::render('ecommerce/orders'))->name('dashboard.ecommerce.orders');
Route::get('/products', fn () => Inertia::render('ecommerce/products'))->name('dashboard.ecommerce.products');
Route::get('/products/edit', fn () => Inertia::render('ecommerce/product'))->name('dashboard.ecommerce.products.edit');
Route::get('/tasks', fn () => Inertia::render('tasks/index'))->name('dashboard.tasks');
Route::resource('/users', \App\Http\Controllers\UserController::class)->names([
    'index' => 'dashboard.users',
    'store' => 'dashboard.users.store',
    'update' => 'dashboard.users.update',
    'destroy' => 'dashboard.users.destroy',
]);
Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
Route::post('/profile/with-avatar', [ProfileController::class, 'updateWithAvatar'])->name('profile.update.with.avatar');
Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
Route::get('/help-center', fn () => Inertia::render('coming-soon/index'))->name('dashboard.coming-soon');
Route::get('/chat-ai', fn () => Inertia::render('playground/dashboard-03'))->name('dashboard.03');
Route::get('/job-search', function () {
    $types = PositionType::all();
    $companies = Company::limit(6)->get();
    $cities = JobOffer::orderByDesc('listedAt')
        ->limit(100)
        ->pluck('city')
        ->groupBy(fn($city) => $city)
        ->map(fn($group) => count($group))
        ->sortDesc()
        ->take(6)
        ->keys()
        ->values();
    $jobOffers = JobOffer::with('company')->orderByDesc('listedAt')->limit(20)->get()->toArray();
    return Inertia::render('job-search/index', [
        'types' => $types,
        'companies' => $companies,
        'cities' => $cities,
        'jobOffers' => $jobOffers,
    ]);
})->name('dashboard.job-search');
Route::get('/preferences', fn () => Inertia::render('preferences/index'))->name('dashboard.preferences');

// Administration routes
Route::group(['prefix' => '/admin'], function () {
    Route::get('/job-offers', function () {
        $jobOffers = JobOffer::with(['company', 'positionType'])->orderByDesc('listedAt')->get();
        $companies = Company::all();
        $positionTypes = PositionType::all();
        return Inertia::render('admin/job-offers/index', [
            'jobOffers' => $jobOffers,
            'companies' => $companies,
            'positionTypes' => $positionTypes,
        ]);
    })->name('dashboard.admin.job-offers');

    Route::get('/companies', function () {
        $companies = Company::orderBy('name')->get();
        return Inertia::render('admin/companies/index', [
            'companies' => $companies,
        ]);
    })->name('dashboard.admin.companies');

    Route::get('/position-types', function () {
        $positionTypes = PositionType::orderBy('name')->get();
        return Inertia::render('admin/position-types/index', [
            'positionTypes' => $positionTypes,
        ]);
    })->name('dashboard.admin.position-types');
});

// Route supprimée car la page de CV est maintenant la page principale du tableau de bord
