import React, { useState, useEffect } from "react"
import { AuthenticatedLayout } from "@/layouts"
import { Main } from "@/components/layout/main"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, MapPin, Calendar, MessageCircle, Share2 } from "lucide-react"
import { usePage } from '@inertiajs/react';
import axios from 'axios';
import { formatDistanceToNow, parseISO } from 'date-fns';

// Types
interface JobOffer {
  id: number
  title: string
  company: { id: number; name: string }
  city: string
  country: string
  listedAt: string
  description: string
  tags: string[]
  prerequisites: string
  responsabilities: string
}

// Composant pour les filtres
const FilterSection = ({ title, filters, selected, onChange }: { title: string, filters: string[], selected: string[], onChange: (value: string) => void }) => {
  return (
    <div className="mb-6">
      <h3 className="font-medium mb-2">{title}</h3>
      <div className="space-y-2">
        {filters.map((filter, index) => (
          <div key={index} className="flex items-center">
            <Checkbox id={`${title.toLowerCase()}-${index}`} className="mr-2" checked={selected.includes(filter)} onCheckedChange={() => onChange(filter)} />
            <label htmlFor={`${title.toLowerCase()}-${index}`} className="text-sm">
              {filter}
            </label>
          </div>
        ))}
      </div>
    </div>
  )
}

// Composant pour la carte d'offre d'emploi détaillée
const JobDetailCard = ({ job }: { job: JobOffer }) => {
  return (
    <Card className="p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-xl font-semibold">{job.title}</h2>
          <p className="text-muted-foreground">{job.company?.name} • {job.city}, {job.country}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Partager
          </Button>
          <Button size="sm">
            Postuler
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        {job.tags.map((skill, index) => (
          <Badge key={index} variant="secondary">{skill}</Badge>
        ))}
      </div>

      <div className="mb-6">
        <h3 className="font-semibold mb-2">Description du poste</h3>
        <p className="text-sm text-muted-foreground">{job.description}</p>
      </div>

      <div className="mb-6">
        <h3 className="font-semibold mb-2">Prérequis</h3>
        <ul className="list-disc pl-5 text-sm text-muted-foreground">
          {job.prerequisites.split('\n').map((req, index) => (
            <li key={index}>{req}</li>
          ))}
        </ul>
      </div>

      <div className="mb-6">
        <h3 className="font-semibold mb-2">Responsabilités</h3>
        <ul className="list-disc pl-5 text-sm text-muted-foreground">
          {job.responsabilities.split('\n').map((resp, index) => (
            <li key={index}>{resp}</li>
          ))}
        </ul>
      </div>

      <div className="flex justify-end">
        <Button className="w-full sm:w-auto">
          Postuler maintenant
        </Button>
      </div>
    </Card>
  )
}

// Composant pour la carte d'offre d'emploi dans la liste
const JobListItem = ({ job, isSelected, onClick }: { job: JobOffer, isSelected: boolean, onClick: () => void }) => {
  // Format date in client local time and nice format
  let formattedDate = '';
  try {
    formattedDate = formatDistanceToNow(parseISO(job.listedAt), { addSuffix: true });
  } catch {
    formattedDate = new Date(job.listedAt).toLocaleString();
  }
  return (
    <div className={`p-4 border-b cursor-pointer hover:bg-muted/50 ${isSelected ? 'bg-muted' : ''}`} onClick={onClick}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium">{job.title}</h3>
          <p className="text-sm text-muted-foreground">{job.company?.name} • {job.city}, {job.country}</p>
        </div>
        <Checkbox checked={isSelected} />
      </div>
      <div className="flex items-center text-xs text-muted-foreground mt-2">
        <Calendar className="h-3 w-3 mr-1" />
        <span>{formattedDate}</span>
      </div>
    </div>
  )
}

// Page principale
export default function JobSearchPage() {
  const { types = [], companies = [], cities = [] } = usePage().props as any;
  const [selectedJob, setSelectedJob] = useState<JobOffer | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [jobOffers, setJobOffers] = useState<JobOffer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch job offers from backend
  useEffect(() => {
    const fetchJobs = async () => {
      setLoading(true);
      setError(null);
      try {
        const params: any = {
          page,
          search: searchQuery,
        };
        if (selectedTypes.length > 0) params.types = selectedTypes;
        if (selectedCompanies.length > 0) params.companies = selectedCompanies;
        if (selectedCities.length > 0) params.cities = selectedCities;
        const response = await axios.get('/api/job-offers', { params });
        setJobOffers(response.data.data);
        setTotalPages(response.data.last_page || 1);
      } catch (e: any) {
        setError(e.message || 'Erreur lors du chargement des offres');
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
    // eslint-disable-next-line
  }, [searchQuery, selectedTypes, selectedCompanies, selectedCities, page]);

  // When jobOffers changes, select the first job in the list
  useEffect(() => {
    if (jobOffers.length > 0) {
      setSelectedJob(jobOffers[0]);
    } else {
      setSelectedJob(null);
    }
  }, [jobOffers]);

  // Use props for filters
  const typeFilters = types.map((t: any) => t.name);
  const locationFilters = cities;
  const companyFilters = companies.map((c: any) => c.name);

  const handleTypeChange = (type: string) => {
    setSelectedTypes((prev) => prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]);
    setPage(1);
  };
  const handleCompanyChange = (company: string) => {
    setSelectedCompanies((prev) => prev.includes(company) ? prev.filter(c => c !== company) : [...prev, company]);
    setPage(1);
  };
  const handleCityChange = (city: string) => {
    setSelectedCities((prev) => prev.includes(city) ? prev.filter(c => c !== city) : [...prev, city]);
    setPage(1);
  };

  return (
    <AuthenticatedLayout title="Chercher des offres">
      <Main>
        <div className="container mx-auto py-6">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            {/* Sidebar avec filtres */}
            <div className="md:col-span-2">
              <Card className="p-4">
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Rechercher..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => { setSearchQuery(e.target.value); setPage(1); }}
                    />
                  </div>
                  <div className="mt-2 text-xs text-muted-foreground">
                    <span>Exemple: </span>
                    <span className="font-medium">designer ou #paris</span>
                  </div>
                </div>

                <ScrollArea className="h-[calc(100vh-220px)]">
                  <FilterSection title="Type" filters={typeFilters} selected={selectedTypes} onChange={handleTypeChange} />
                  <Separator className="my-4" />
                  <FilterSection title="Localité" filters={locationFilters} selected={selectedCities} onChange={handleCityChange} />
                  <Separator className="my-4" />
                  <FilterSection title="Entreprise" filters={companyFilters} selected={selectedCompanies} onChange={handleCompanyChange} />
                </ScrollArea>
              </Card>
            </div>

            {/* Liste des offres */}
            <div className="md:col-span-3">
              <Card className="overflow-hidden">
                <div className="p-4 border-b">
                  <div className="relative mb-3">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="ecofriendlyjobs ou #ecofriendlyfutures"
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => { setSearchQuery(e.target.value); setPage(1); }}
                    />
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{jobOffers.length} offres trouvées</span>
                    <Button variant="outline" size="sm" disabled>
                      Trier par <span className="font-medium ml-1">Date</span>
                    </Button>
                  </div>
                </div>
                <ScrollArea className="h-[calc(100vh-220px)]">
                  {loading ? (
                    <div className="p-4 text-center">Chargement...</div>
                  ) : error ? (
                    <div className="p-4 text-center text-red-500">{error}</div>
                  ) : jobOffers.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">Aucune offre trouvée</div>
                  ) : (
                    jobOffers.map((job: JobOffer) => (
                      <JobListItem
                        key={job.id}
                        job={job}
                        isSelected={selectedJob?.id === job.id}
                        onClick={() => setSelectedJob(job)}
                      />
                    ))
                  )}
                </ScrollArea>
                {/* Pagination */}
                <div className="flex justify-center items-center gap-2 p-2">
                  <Button size="sm" variant="outline" disabled={page <= 1} onClick={() => setPage(page - 1)}>Précédent</Button>
                  <span>Page {page} / {totalPages}</span>
                  <Button size="sm" variant="outline" disabled={page >= totalPages} onClick={() => setPage(page + 1)}>Suivant</Button>
                </div>
              </Card>
            </div>

            {/* Détail de l'offre */}
            <div className="md:col-span-7">
              <ScrollArea className="h-[calc(100vh-160px)]">
                {selectedJob ? (
                  <JobDetailCard job={selectedJob} />
                ) : (
                  <Card className="p-6 text-center">
                    <p className="text-muted-foreground">Sélectionnez une offre pour voir les détails</p>
                  </Card>
                )}
              </ScrollArea>
            </div>
          </div>
        </div>
      </Main>
    </AuthenticatedLayout>
  )
}
