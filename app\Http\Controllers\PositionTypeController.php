<?php

namespace App\Http\Controllers;

use App\Models\PositionType;
use Illuminate\Http\Request;

class PositionTypeController extends Controller
{
    public function index()
    {
        return PositionType::all();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string|max:255|unique:position_types,code',
            'name' => 'required|string|max:255',
        ]);
        return PositionType::create($validated);
    }

    public function show(PositionType $positionType)
    {
        return $positionType;
    }

    public function update(Request $request, PositionType $positionType)
    {
        $validated = $request->validate([
            'code' => 'required|string|max:255|unique:position_types,code,' . $positionType->id,
            'name' => 'required|string|max:255',
        ]);
        $positionType->update($validated);
        return $positionType;
    }

    public function destroy(PositionType $positionType)
    {
        $positionType->delete();
        return response()->noContent();
    }
} 