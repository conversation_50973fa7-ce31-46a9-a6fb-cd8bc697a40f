import { AuthenticatedLayout } from "@/layouts"
import { Main } from '@/components/layout/main'
import { columns } from './components/position-types-columns'
import { PositionTypesDialogs } from './components/position-types-dialogs'
import { PositionTypesPrimaryButtons } from './components/position-types-primary-buttons'
import { PositionTypesTable } from './components/position-types-table'
import PositionTypesProvider from './context/position-types-context'
import { positionTypeListSchema } from './data/schema'

interface Props {
  positionTypes: any[]
}

export default function PositionTypesAdmin({ positionTypes = [] }: Props) {
  // Parse position types list from the backend
  const positionTypesList = positionTypeListSchema.parse(positionTypes)

  return (
    <PositionTypesProvider>
      <AuthenticatedLayout title={"Administration - Types de postes"}>
        <Main>
          <div className='mb-2 flex items-center justify-between space-y-2 flex-wrap'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Types de postes</h2>
              <p className='text-muted-foreground'>
                <PERSON><PERSON><PERSON> les types de postes et leurs informations.
              </p>
            </div>
            <PositionTypesPrimaryButtons />
          </div>
          <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
            <PositionTypesTable 
              data={positionTypesList} 
              columns={columns}
            />
          </div>
        </Main>

        <PositionTypesDialogs />
      </AuthenticatedLayout>
    </PositionTypesProvider>
  )
}
