import { IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { usePositionTypes } from '../context/position-types-context'

export function PositionTypesPrimaryButtons() {
  const { setOpen } = usePositionTypes()

  return (
    <div className='flex items-center space-x-2'>
      <Button
        size='sm'
        onClick={() => setOpen('add')}
        className='h-8'
      >
        <IconPlus className='mr-2 h-4 w-4' />
        Ajouter un type de poste
      </Button>
    </div>
  )
}
