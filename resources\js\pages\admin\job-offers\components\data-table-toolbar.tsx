import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cities, countries } from '../data/data'
import { DataTableFacetedFilter } from './data-table-faceted-filter'
import { DataTableViewOptions } from './data-table-view-options'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  companies: Array<{ label: string; value: string }>
  positionTypes: Array<{ label: string; value: string }>
}

export function DataTableToolbar<TData>({
  table,
  companies,
  positionTypes,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2'>
        <Input
          placeholder='Filtrer les offres...'
          value={(table.getColumn('title')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('title')?.setFilterValue(event.target.value)
          }
          className='h-8 w-[150px] lg:w-[250px]'
        />
        <div className='flex gap-x-2'>
          {table.getColumn('company') && (
            <DataTableFacetedFilter
              column={table.getColumn('company')}
              title='Entreprise'
              options={companies}
            />
          )}
          {table.getColumn('city') && (
            <DataTableFacetedFilter
              column={table.getColumn('city')}
              title='Ville'
              options={cities}
            />
          )}
          {table.getColumn('positionType') && (
            <DataTableFacetedFilter
              column={table.getColumn('positionType')}
              title='Type de poste'
              options={positionTypes}
            />
          )}
          {isFiltered && (
            <Button
              variant='ghost'
              onClick={() => table.resetColumnFilters()}
              className='h-8 px-2 lg:px-3'
            >
              Réinitialiser
              <Cross2Icon className='ml-2 h-4 w-4' />
            </Button>
          )}
        </div>
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
