<?php

namespace App\Http\Controllers;

use App\Models\JobOffer;
use Illuminate\Http\Request;

class JobOfferController extends Controller
{


    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company_id' => 'required|exists:companies,id',
            'tags' => 'nullable|array',
            'tags.*' => 'string',
            'description' => 'required|string',
            'prerequisites' => 'nullable|string',
            'responsabilities' => 'nullable|string',
            'position_type_id' => 'required|exists:position_types,id',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'listedAt' => 'required|date',
        ]);
        $validated['tags'] = $request->tags ?? [];
        return JobOffer::create($validated);
    }

    public function show(JobOffer $jobOffer)
    {
        return $jobOffer->load(['company', 'positionType']);
    }

    public function update(Request $request, JobOffer $jobOffer)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company_id' => 'required|exists:companies,id',
            'tags' => 'nullable|array',
            'tags.*' => 'string',
            'description' => 'required|string',
            'prerequisites' => 'nullable|string',
            'responsabilities' => 'nullable|string',
            'position_type_id' => 'required|exists:position_types,id',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'listedAt' => 'required|date',
        ]);
        $validated['tags'] = $request->tags ?? [];
        $jobOffer->update($validated);
        return $jobOffer->load(['company', 'positionType']);
    }

    public function destroy(JobOffer $jobOffer)
    {
        $jobOffer->delete();
        return response()->noContent();
    }

    public function index(Request $request)
    {
        $query = JobOffer::with('company');

        if ($request->has('types') && is_array($request->types)) {
            $query->whereHas('positionType', function ($q) use ($request) {
                $q->whereIn('name', $request->types);
            });
        }
        if ($request->has('companies') && is_array($request->companies)) {
            $query->whereHas('company', function ($q) use ($request) {
                $q->whereIn('name', $request->companies);
            });
        }
        if ($request->has('cities') && is_array($request->cities)) {
            $query->whereIn('city', $request->cities);
        }
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%$search%")
                  ->orWhere('description', 'like', "%$search%")
                  ->orWhere('city', 'like', "%$search%")
                  ->orWhere('country', 'like', "%$search%")
                  ->orWhereHas('company', function ($q2) use ($search) {
                      $q2->where('name', 'like', "%$search%") ;
                  });
            });
        }

        $jobOffers = $query->orderByDesc('listedAt')->paginate(10);
        return response()->json($jobOffers);
    }
} 