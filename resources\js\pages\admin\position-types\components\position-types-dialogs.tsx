import { usePositionTypes } from '../context/position-types-context'
import { PositionTypesActionDialog } from './position-types-action-dialog'
import { PositionTypesDeleteDialog } from './position-types-delete-dialog'

export function PositionTypesDialogs() {
  const { open, setOpen, currentRow } = usePositionTypes()

  return (
    <>
      <PositionTypesActionDialog
        key={`action-${currentRow?.id || 'new'}`}
        currentRow={open === 'edit' ? currentRow! : undefined}
        open={open === 'add' || open === 'edit'}
        onOpenChange={(state) => !state && setOpen(null)}
      />

      {currentRow && (
        <PositionTypesDeleteDialog
          key={`delete-${currentRow.id}`}
          open={open === 'delete'}
          onOpenChange={(state) => !state && setOpen(null)}
          currentRow={currentRow}
        />
      )}
    </>
  )
}
