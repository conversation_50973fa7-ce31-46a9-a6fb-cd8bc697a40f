import {
  IconBarrierBlock,
  IconBrowserCheck,
  IconBug,
  IconChecklist,
  IconError404,
  IconHelp,
  IconLayoutDashboard,
  IconLock,
  IconLockAccess,
  IconMessages,
  IconNotification,
  IconPackages,
  IconPalette,
  IconServerOff,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUserOff,
  IconUsers,
  IconChartBar,
  IconShoppingBag,
  IconShoppingBagDiscount,
  IconShoe,
  IconRobot,
  IconSearch,
  IconBriefcase,
  IconAdjustments,
  IconBuilding,
  IconCategory,
  IconShield,
} from '@tabler/icons-react'
import {
  AudioWaveform,
  Command,
  GalleryVerticalEnd
} from 'lucide-react'
import { type SidebarData } from '../types'

export const sidebarData: SidebarData = {
  user: {
    name: 'binjuhor',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'Shadcn Admin',
      logo: Command,
      plan: 'Vite + ShadcnUI',
    },
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
  ],
  navGroups: [
    {
      title: 'General',
      items: [
        {
          title: 'Résumé de profil',
          url: '/dashboard',
          icon: IconLayoutDashboard,
        },
        {
          title: 'Chercher des offres',
          url: '/dashboard/job-search',
          icon: IconBriefcase,
        },
        {
          title: 'Préférences',
          url: '/dashboard/preferences',
          icon: IconAdjustments,
        },

      ],
    },
    {
      title: 'Administration',
      items: [
        {
          title: 'Gestion des emplois',
          icon: IconBriefcase,
          items: [
            {
              title: 'Offres d\'emploi',
              url: '/dashboard/admin/job-offers',
              icon: IconBriefcase,
            },
            {
              title: 'Entreprises',
              url: '/dashboard/admin/companies',
              icon: IconBuilding,
            },
            {
              title: 'Types de postes',
              url: '/dashboard/admin/position-types',
              icon: IconCategory,
            },
          ],
        },
      ],
    },
    {
      title: 'Template pages',
      items: [
        {
          title: 'Tasks',
          url: '/dashboard/tasks',
          icon: IconChecklist,
        },
        {
          title: 'Apps',
          icon: IconPackages,
          items: [
            {
              title: 'Chats',
              url: '/dashboard/chats',
              icon: IconMessages,
              badge: '3',
            },
            {
              title: 'Ai Chats',
              url: '/dashboard/chat-ai',
              icon: IconRobot,
            },
          ],
        },
        {
          title: 'Charts',
          url: '/dashboard/charts',
          icon: IconChartBar,
        },
        {
          title: 'Users',
          url: '/dashboard/users',
          icon: IconUsers,
        },
        {
          title: 'Ecommerce',
          icon: IconShoppingBag,
          items: [
            {
              title: 'Orders',
              url: '/dashboard/orders',
              icon: IconShoppingBagDiscount,
            },
            {
              title: 'Products',
              url: '/dashboard/products',
              icon: IconShoe,
            },
            {
              title: 'Product',
              url: '/dashboard/products/edit',
              icon: IconShoe,
            },
          ],
        },
        {
          title: 'Pages',
          icon: IconLockAccess,
          items: [
            {
              title: 'Auth',
              icon: IconLockAccess,
              items: [
                {
                  title: 'Sign In',
                  url: '/sign-in',
                },
                {
                  title: 'Sign In (2 Col)',
                  url: '/sign-in-2',
                },
                {
                  title: 'Sign Up',
                  url: '/sign-up',
                },
                {
                  title: 'Forgot Password',
                  url: '/forgot-pass',
                },
                {
                  title: 'OTP',
                  url: '/otp',
                },
              ],
            },
            {
              title: 'Errors',
              icon: IconBug,
              items: [
                {
                  title: 'Unauthorized',
                  url: '/401',
                  icon: IconLock,
                },
                {
                  title: 'Forbidden',
                  url: '/403',
                  icon: IconUserOff,
                },
                {
                  title: 'Not Found',
                  url: '/404',
                  icon: IconError404,
                },
                {
                  title: 'Internal Server Error',
                  url: '/500',
                  icon: IconServerOff,
                },
                {
                  title: 'Maintenance Error',
                  url: '/503',
                  icon: IconBarrierBlock,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      title: 'Other',
      items: [
        {
          title: 'Settings',
          icon: IconSettings,
          items: [
            {
              title: 'Profile',
              url: '/dashboard/settings',
              icon: IconUserCog,
            },
            {
              title: 'Account',
              url: '/dashboard/settings/account',
              icon: IconTool,
            },
            {
              title: 'Appearance',
              url: '/dashboard/settings/appearance',
              icon: IconPalette,
            },
            {
              title: 'Notifications',
              url: '/dashboard/settings/notifications',
              icon: IconNotification,
            },
            {
              title: 'Display',
              url: '/dashboard/settings/display',
              icon: IconBrowserCheck,
            },
          ],
        },
        {
          title: 'Help Center',
          url: '/dashboard/help-center',
          icon: IconHelp,
        },
      ],
    },
  ],
}
