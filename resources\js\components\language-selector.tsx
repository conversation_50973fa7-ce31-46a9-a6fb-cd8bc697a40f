import React from 'react';
import { useLanguage } from '@/context/language-context';
import { router } from '@inertiajs/react';
import { changeLanguage as i18nChangeLanguage } from '@/lib/i18n';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function LanguageSelector() {
  const { currentLocale, availableLocales, changeLanguage, isLoading } = useLanguage();

  // Get current language name
  const currentLanguageName = availableLocales.find(locale => locale.code === currentLocale)?.name || 'English';

  console.log('LanguageSelector - Current locale:', currentLocale);
  console.log('LanguageSelector - Available locales:', availableLocales);
  console.log('LanguageSelector - Current language name:', currentLanguageName);

  return (
    <div className="relative">
      <Select
        value={currentLocale}
        onValueChange={(value) => {
          console.log('Language selected:', value);

          // Check if we're on the landing page with a language prefix
          const path = window.location.pathname;
          const isLandingPage = /^\/[a-z]{2}$/.test(path);

          console.log('Language selector - Current path:', path);
          console.log('Language selector - Is landing page:', isLandingPage);
          console.log('Language selector - Selected language:', value);

          if (isLandingPage) {
            // If on landing page, update localStorage first, then navigate
            const newUrl = `/${value}`;
            console.log('Language selector - Navigating to:', newUrl);

            // Update localStorage before navigation
            localStorage.setItem('locale', value);
            localStorage.setItem('i18nextLng', value);

            // Also update i18n language
            i18nChangeLanguage(value);

            // Use replace instead of visit to ensure a full page reload
            window.location.href = newUrl;
          } else {
            // For other pages, use the context's changeLanguage
            console.log('Language selector - Using context changeLanguage');
            changeLanguage(value);
          }
        }}
        disabled={isLoading}
      >
        <SelectTrigger className="w-[110px] h-9">
          <SelectValue placeholder="Select language" />
        </SelectTrigger>
        <SelectContent>
          {availableLocales.map((locale) => (
            <SelectItem key={locale.code} value={locale.code}>
              {locale.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
        </div>
      )}
    </div>
  );
}
