import { AuthenticatedLayout } from "@/layouts"
import { Main } from '@/components/layout/main'
import { columns } from './components/job-offers-columns'
import { JobOffersDialogs } from './components/job-offers-dialogs'
import { JobOffersPrimaryButtons } from './components/job-offers-primary-buttons'
import { JobOffersTable } from './components/job-offers-table'
import JobOffersProvider from './context/job-offers-context'
import { jobOfferListSchema } from './data/schema'
import { usePage } from '@inertiajs/react'

interface Props {
  jobOffers: any[]
  companies: any[]
  positionTypes: any[]
}

export default function JobOffersAdmin({ jobOffers = [], companies = [], positionTypes = [] }: Props) {
  // Parse job offers list from the backend
  const jobOffersList = jobOfferListSchema.parse(jobOffers)

  // Transform companies and position types for filters
  const companiesOptions = companies.map(company => ({
    label: company.name,
    value: company.name,
  }))

  const positionTypesOptions = positionTypes.map(type => ({
    label: type.name,
    value: type.name,
  }))

  return (
    <JobOffersProvider>
      <AuthenticatedLayout title={"Administration - Offres d'emploi"}>
        <Main>
          <div className='mb-2 flex items-center justify-between space-y-2 flex-wrap'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Offres d'emploi</h2>
              <p className='text-muted-foreground'>
                Gérez les offres d'emploi et leurs informations.
              </p>
            </div>
            <JobOffersPrimaryButtons />
          </div>
          <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
            <JobOffersTable 
              data={jobOffersList} 
              columns={columns}
              companies={companiesOptions}
              positionTypes={positionTypesOptions}
            />
          </div>
        </Main>

        <JobOffersDialogs />
      </AuthenticatedLayout>
    </JobOffersProvider>
  )
}
