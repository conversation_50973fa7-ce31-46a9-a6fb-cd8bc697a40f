import React from "react";
import { Link, usePage } from "@inertiajs/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Globe, Briefcase, Users, Award } from "lucide-react";
import { LandingHeader } from "./components/header";
import { LandingFooter } from "./components/footer";
import { FeatureCard } from "./components/feature-card";
import { TestimonialCard } from "./components/testimonial-card";
import { HeroSection } from "./components/hero-section";
import { useTranslation } from "react-i18next";
import { HreflangTags } from "@/components/hreflang-tags";

export default function LandingPage() {
  const { props } = usePage();

  // Use the explicit locale from the route handler
  const locale = props.explicitLocale || props.locale || 'en';

  // Force i18next to use the locale from the route
  const { t, i18n } = useTranslation();

  // Log the current locale for debugging
  console.log('Landing Page - Props:', props);
  console.log('Landing Page - Explicit locale:', props.explicitLocale);
  console.log('Landing Page - Route locale:', locale);
  console.log('Landing Page - i18n current language:', i18n.language);

  // Ensure i18n is using the correct locale
  React.useEffect(() => {
    if (i18n.language !== locale) {
      console.log('Changing i18n language to match route locale:', locale);

      // Update localStorage to match the route locale
      localStorage.setItem('locale', locale);
      localStorage.setItem('i18nextLng', locale);

      // Force language change and reload resources
      i18n.changeLanguage(locale).then(() => {
        console.log('Language changed to:', i18n.language);

        // Force reload of the page to ensure all translations are applied
        if (i18n.language !== locale) {
          console.log('Language still not matching, forcing reload');
          window.location.reload();
        }
      });
    } else {
      // Even if the language matches, ensure localStorage is updated
      const storedLocale = localStorage.getItem('locale');
      const storedI18nLng = localStorage.getItem('i18nextLng');

      if (storedLocale !== locale || storedI18nLng !== locale) {
        console.log('Updating localStorage to match route locale:', locale);
        localStorage.setItem('locale', locale);
        localStorage.setItem('i18nextLng', locale);
      }
    }
  }, [locale, i18n]);
  return (
    <div className="min-h-screen bg-white">
      {/* Add hreflang tags for SEO */}
      <HreflangTags currentLocale={locale} />

      <LandingHeader />

      <main>
        {/* Hero Section */}
        <HeroSection />

        {/* Features Section */}
        <section id="features" className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">{t('How Afeq helps you find a job')}</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('Our platform automates and simplifies every step of your international job search')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <FeatureCard
                icon={<Users className="h-10 w-10 text-[#3D7B9A]" />}
                title={t('Profile and CV creation')}
                description={t('Create a professional profile and generate a CV adapted to international standards')}
              />
              <FeatureCard
                icon={<Briefcase className="h-10 w-10 text-[#3D7B9A]" />}
                title={t('Personalized job search')}
                description={t('Our algorithm finds job offers that perfectly match your profile')}
              />
              <FeatureCard
                icon={<Globe className="h-10 w-10 text-[#3D7B9A]" />}
                title={t('Automatic application')}
                description={t('Apply automatically to selected offers and track your applications')}
              />
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">{t('How it works')}</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('A simple 4-step process to find your international job')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="bg-[#3D7B9A] text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">{t('Create your account')}</h3>
                <p className="text-gray-600">{t('Sign up and complete your profile with your skills and experiences')}</p>
              </div>
              <div className="text-center">
                <div className="bg-[#3D7B9A] text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">{t('Generate your CV')}</h3>
                <p className="text-gray-600">{t('Our assistant helps you create a professional CV adapted to international standards')}</p>
              </div>
              <div className="text-center">
                <div className="bg-[#3D7B9A] text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">{t('Find job offers')}</h3>
                <p className="text-gray-600">{t('Our algorithm searches for offers that match your profile')}</p>
              </div>
              <div className="text-center">
                <div className="bg-[#3D7B9A] text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">4</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">{t('Apply and track')}</h3>
                <p className="text-gray-600">{t('Apply automatically and track your applications directly on the platform')}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">{t('What our users say')}</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('Discover how Afeq has helped young people find international jobs')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <TestimonialCard
                quote={t('Thanks to Afeq, I found a job in France in just 3 weeks. The process was simple and efficient.')}
                author="Sarah B."
                role={t('Web Developer')}
                location={t('Paris, France')}
              />
              <TestimonialCard
                quote={t('The automatic CV generation saved me a lot of time, and the interview tips were very helpful.')}
                author="Mohamed A."
                role={t('Software Engineer')}
                location={t('Montreal, Canada')}
              />
              <TestimonialCard
                quote={t('I received several job offers perfectly matching my profile. Afeq really understood my skills.')}
                author="Leila K."
                role={t('Cloud Architect')}
                location={t('Berlin, Germany')}
              />
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="pricing" className="py-20 bg-[#3D7B9A]">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-white mb-6">{t('Ready to start your international career?')}</h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
              {t('Join thousands of young people who have already found their job through Afeq')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-[#3D7B9A] hover:bg-white/90">
                <Link href="/register">{t('Create an account')}</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white/10 bg-transparent">
                <Link href="/login">{t('Log in')}</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>

      <LandingFooter />
    </div>
  );
}
