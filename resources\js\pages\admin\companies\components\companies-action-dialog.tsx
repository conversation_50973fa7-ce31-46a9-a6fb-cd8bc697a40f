'use client'

import { useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Company } from '../data/schema'
import axios from 'axios'
import { router } from '@inertiajs/react'

const formSchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  introduction: z.string().optional(),
})

type CompanyForm = z.infer<typeof formSchema>

interface Props {
  currentRow?: Company
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CompaniesActionDialog({ currentRow, open, onOpenChange }: Props) {
  const isEdit = !!currentRow
  const [loading, setLoading] = useState(false)

  const form = useForm<CompanyForm>({
    resolver: zodResolver(formSchema),
    defaultValues: isEdit
      ? {
          name: currentRow.name,
          introduction: currentRow.introduction || '',
        }
      : {
          name: '',
          introduction: '',
        },
  })

  const onSubmit = async (values: CompanyForm) => {
    setLoading(true)
    try {
      const data = {
        ...values,
        introduction: values.introduction || null,
      }

      if (isEdit) {
        await axios.put(`/api/companies/${currentRow.id}`, data)
        toast({
          title: 'Succès',
          description: 'Entreprise modifiée avec succès',
        })
      } else {
        await axios.post('/api/companies', data)
        toast({
          title: 'Succès',
          description: 'Entreprise créée avec succès',
        })
      }

      form.reset()
      onOpenChange(false)
      router.reload()
    } catch (error: any) {
      console.error('Error saving company:', error)
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (!loading) {
          form.reset()
          onOpenChange(state)
        }
      }}
    >
      <DialogContent className='sm:max-w-lg'>
        <DialogHeader className='text-left'>
          <DialogTitle>{isEdit ? "Modifier l'entreprise" : "Ajouter une entreprise"}</DialogTitle>
          <DialogDescription>
            {isEdit ? "Modifiez les informations de l'entreprise." : "Créez une nouvelle entreprise."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id='company-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='space-y-4'
          >
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nom de l'entreprise"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="introduction"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Description de l'entreprise..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            form="company-form"
            disabled={loading}
          >
            {loading ? "Enregistrement..." : (isEdit ? "Modifier" : "Créer")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
