import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import LongText from '@/components/long-text'
import { JobOffer } from '../data/schema'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<JobOffer>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Titre' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[200px] truncate font-medium'>
            {row.getValue('title')}
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: 'company',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Entreprise' />
    ),
    cell: ({ row }) => {
      const company = row.original.company
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[150px] truncate'>
            {company?.name || 'N/A'}
          </span>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.original.company?.name)
    },
  },
  {
    accessorKey: 'city',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Ville' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[100px] truncate'>
            {row.getValue('city')}
          </span>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'positionType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Type de poste' />
    ),
    cell: ({ row }) => {
      const positionType = row.original.positionType
      return (
        <div className='flex space-x-2'>
          <Badge variant='outline'>
            {positionType?.name || 'N/A'}
          </Badge>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.original.positionType?.name)
    },
  },
  {
    accessorKey: 'tags',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Compétences' />
    ),
    cell: ({ row }) => {
      const tags = row.getValue('tags') as string[]
      return (
        <div className='flex space-x-1'>
          {tags?.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant='secondary' className='text-xs'>
              {tag}
            </Badge>
          ))}
          {tags?.length > 2 && (
            <Badge variant='secondary' className='text-xs'>
              +{tags.length - 2}
            </Badge>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'listedAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Date de publication' />
    ),
    cell: ({ row }) => {
      const date = row.getValue('listedAt') as Date
      return (
        <div className='flex space-x-2'>
          <span className='text-sm text-muted-foreground'>
            {date.toLocaleDateString('fr-FR')}
          </span>
        </div>
      )
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
    meta: {
      className: cn(
        'sticky right-0 z-10 rounded-tr',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
  },
]
