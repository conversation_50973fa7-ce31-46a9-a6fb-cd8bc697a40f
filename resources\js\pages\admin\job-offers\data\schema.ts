import { z } from 'zod'

export const jobOfferSchema = z.object({
  id: z.number(),
  title: z.string(),
  company_id: z.number(),
  company: z.object({
    id: z.number(),
    name: z.string(),
    introduction: z.string().nullable(),
  }).optional(),
  tags: z.array(z.string()),
  description: z.string(),
  prerequisites: z.string().nullable(),
  responsabilities: z.string().nullable(),
  position_type_id: z.number(),
  positionType: z.object({
    id: z.number(),
    code: z.string(),
    name: z.string(),
  }).optional(),
  city: z.string(),
  country: z.string(),
  listedAt: z.coerce.date(),
  created_at: z.coerce.date(),
  updated_at: z.coerce.date(),
})

export type JobOffer = z.infer<typeof jobOfferSchema>

export const jobOfferListSchema = z.array(jobOfferSchema)

export const companySchema = z.object({
  id: z.number(),
  name: z.string(),
  introduction: z.string().nullable(),
})

export type Company = z.infer<typeof companySchema>

export const positionTypeSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: z.string(),
})

export type PositionType = z.infer<typeof positionTypeSchema>
