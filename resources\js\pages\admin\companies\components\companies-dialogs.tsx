import { useCompanies } from '../context/companies-context'
import { CompaniesActionDialog } from './companies-action-dialog'
import { CompaniesDeleteDialog } from './companies-delete-dialog'

export function CompaniesDialogs() {
  const { open, setOpen, currentRow } = useCompanies()

  return (
    <>
      <CompaniesActionDialog
        key={`action-${currentRow?.id || 'new'}`}
        currentRow={open === 'edit' ? currentRow! : undefined}
        open={open === 'add' || open === 'edit'}
        onOpenChange={(state) => !state && setOpen(null)}
      />

      {currentRow && (
        <CompaniesDeleteDialog
          key={`delete-${currentRow.id}`}
          open={open === 'delete'}
          onOpenChange={(state) => !state && setOpen(null)}
          currentRow={currentRow}
        />
      )}
    </>
  )
}
