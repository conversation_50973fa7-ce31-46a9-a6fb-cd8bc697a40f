import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CircularProgress } from "./circular-progress"
import { useTranslation } from 'react-i18next'

interface SidebarProps {
  completionPercentage: number;
}

export function ResumeSidebar({ completionPercentage }: SidebarProps) {
  const { t } = useTranslation()

  return (
    <Card className="p-6 shadow-md border-t-4 border-primary">
      <div className="flex flex-col items-center space-y-4">
        <div className="bg-background p-3 rounded-full shadow-md hover:shadow-lg transition-shadow duration-300">
          <CircularProgress
            value={completionPercentage}
            size={120}
            strokeWidth={14}
            color="hsl(var(--primary))"
            backgroundColor="hsl(var(--muted))"
            textColor="hsl(var(--primary))"
          />
        </div>

        <h3 className="text-center text-base font-semibold text-primary cursor-pointer hover:underline transition-all duration-300">
          {t("Complétion du Profil")}
        </h3>
        <p className="text-center text-sm text-muted-foreground">
          {t("Votre taux actuel de complétion du CV est de")} <span className="font-medium">{completionPercentage}%</span>.
          {t("Complétez les sections manquantes pour améliorer votre visibilité auprès des recruteurs.")}
        </p>
      </div>

      <div className="mt-8 space-y-3">
        <Button variant="outline" className="w-full justify-start hover:bg-primary/10 hover:text-primary transition-colors">
          <span className="mr-2 text-lg">📋</span> {t("Voir le CV")}
        </Button>
        <Button variant="outline" className="w-full justify-start hover:bg-primary/10 hover:text-primary transition-colors">
          <span className="mr-2 text-lg">👤</span> {t("Gérer le Profil")}
        </Button>
        <Button variant="outline" className="w-full justify-start hover:bg-primary/10 hover:text-primary transition-colors">
          <span className="mr-2 text-lg">📊</span> {t("Statistiques CV")}
        </Button>
        <Button variant="outline" className="w-full justify-start hover:bg-primary/10 hover:text-primary transition-colors">
          <span className="mr-2 text-lg">🔍</span> {t("Recherche d'emploi")}
        </Button>
      </div>
    </Card>
  )
}
