import { AuthenticatedLayout } from "@/layouts"
import { Main } from '@/components/layout/main'
import { columns } from './components/companies-columns'
import { CompaniesDialogs } from './components/companies-dialogs'
import { CompaniesPrimaryButtons } from './components/companies-primary-buttons'
import { CompaniesTable } from './components/companies-table'
import CompaniesProvider from './context/companies-context'
import { companyListSchema } from './data/schema'

interface Props {
  companies: any[]
}

export default function CompaniesAdmin({ companies = [] }: Props) {
  // Parse companies list from the backend
  const companiesList = companyListSchema.parse(companies)

  return (
    <CompaniesProvider>
      <AuthenticatedLayout title={"Administration - Entreprises"}>
        <Main>
          <div className='mb-2 flex items-center justify-between space-y-2 flex-wrap'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Entreprises</h2>
              <p className='text-muted-foreground'>
                G<PERSON>rez les entreprises et leurs informations.
              </p>
            </div>
            <CompaniesPrimaryButtons />
          </div>
          <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
            <CompaniesTable 
              data={companiesList} 
              columns={columns}
            />
          </div>
        </Main>

        <CompaniesDialogs />
      </AuthenticatedLayout>
    </CompaniesProvider>
  )
}
