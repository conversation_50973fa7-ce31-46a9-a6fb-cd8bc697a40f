<?php

use App\Http\Controllers\EducationController;
use App\Http\Controllers\ExperienceController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\ResumeController;
use App\Http\Controllers\SkillController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\PositionTypeController;
use App\Http\Controllers\JobOfferController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Route de test pour vérifier l'authentification
// Utiliser explicitement le middleware web pour avoir accès à la session
Route::middleware('web')->get('/auth-check', function () {
    return response()->json([
        'authenticated' => Auth::check(),
        'user' => Auth::user() ? ["id" => Auth::id(), "name" => Auth::user()->name] : null,
    ]);
});

// Route de test pour vérifier l'authentification avec le middleware auth
Route::middleware(['web', 'auth'])->get('/auth-check-protected', function () {
    return response()->json([
        'authenticated' => true,
        'user' => ["id" => Auth::id(), "name" => Auth::user()->name],
        'message' => 'Cette route est protégée et n\'est accessible que si vous êtes authentifié'
    ]);
});

// Routes du CV protégées par l'authentification
// Utiliser explicitement les middlewares web et auth
Route::middleware(['web', 'auth'])->group(function () {
    // Routes du CV - accessibles uniquement pour l'utilisateur authentifié
    Route::get('/resume', [ResumeController::class, 'index']);
    Route::put('/resume', [ResumeController::class, 'update']);

    // Skills routes
    Route::apiResource('skills', SkillController::class);

    // Experiences routes
    Route::apiResource('experiences', ExperienceController::class);

    // Education routes
    Route::apiResource('educations', EducationController::class);

    // Languages routes
    Route::apiResource('languages', LanguageController::class);

    // Companies routes 
    Route::apiResource('companies', CompanyController::class);

    // Position types routes
    Route::apiResource('position-types', PositionTypeController::class);

    // Job offers routes
    Route::apiResource('job-offers', JobOfferController::class);
});

