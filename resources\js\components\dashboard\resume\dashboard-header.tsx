import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTranslation } from 'react-i18next'

type DashboardHeaderItemProps = {
  title: string
  subtitle: string
  count: number
  active?: boolean
}

function DashboardHeaderItem({ title, subtitle, count, active = false }: DashboardHeaderItemProps) {
  const { t } = useTranslation()
  return (
    <div className={`flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors ${active ? 'bg-primary/10' : 'hover:bg-muted'}`}>
      <div className="flex-1">
        <h3 className="text-sm font-medium">{t(title)}</h3>
        <p className="text-xs text-muted-foreground">{t(subtitle)}</p>
      </div>
      <Badge variant={active ? "default" : "outline"} className="rounded-full">
        {count}
      </Badge>
    </div>
  )
}

export function DashboardHeader() {
  return (
    <Card className="mb-6 grid grid-cols-1 gap-2 p-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
      <DashboardHeaderItem 
        title="Mes Candidatures" 
        subtitle="Un aperçu rapide" 
        count={3} 
        active={true} 
      />
      <DashboardHeaderItem 
        title="Offres Recommandées" 
        subtitle="Offres personnalisées" 
        count={12} 
      />
      <DashboardHeaderItem 
        title="Entretiens Réalisés" 
        subtitle="Offres récentes acceptées" 
        count={2} 
      />
      <DashboardHeaderItem 
        title="Candidatures en cours" 
        subtitle="Ongoing Process" 
        count={5} 
      />
      <DashboardHeaderItem 
        title="Mes Candidatures" 
        subtitle="All Candidatures" 
        count={8} 
      />
    </Card>
  )
}
