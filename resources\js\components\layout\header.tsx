import React from 'react'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { useEffect, useState } from 'react'

interface HeaderProps extends React.HTMLAttributes<HTMLElement> {
    fixed?: boolean
    ref?: React.Ref<HTMLElement>
}

export const Header = ({
       className,
       fixed,
       children,
       ...props
    }: HeaderProps) => {
    const [offset, setOffset] = React.useState(0)
    const [isRtl, setIsRtl] = useState(false)

    // Check for RTL direction
    useEffect(() => {
        // Set initial RTL state
        setIsRtl(document.documentElement.dir === 'rtl')

        // Create a MutationObserver to watch for changes to the dir attribute
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (
                    mutation.type === 'attributes' &&
                    mutation.attributeName === 'dir' &&
                    mutation.target === document.documentElement
                ) {
                    setIsRtl(document.documentElement.dir === 'rtl')
                }
            })
        })

        // Start observing the document element for dir attribute changes
        observer.observe(document.documentElement, { attributes: true })

        // Clean up the observer on component unmount
        return () => observer.disconnect()
    }, [])

    React.useEffect(() => {
        const onScroll = () => {
            setOffset(document.body.scrollTop || document.documentElement.scrollTop)
        }

        // Add scroll listener to the body
        document.addEventListener('scroll', onScroll, { passive: true })

        // Clean up the event listener on unmount
        return () => document.removeEventListener('scroll', onScroll)
    }, [])

    return (
        <header
            className={cn(
                'flex items-center gap-3 sm:gap-4 bg-background p-4 h-16',
                fixed && 'header-fixed peer/header w-[inherit] fixed z-50 rounded-md',
                offset > 10 && fixed ? 'shadow' : 'shadow-none',
                className
            )}
            {...props}
        >
            <SidebarTrigger
                variant='outline'
                className={cn(
                    'scale-125 sm:scale-100',
                    isRtl && 'transform-flip' // Flip the icon for RTL
                )}
            />
            <Separator orientation='vertical' className='h-6' />
            <div className={cn(
                'flex flex-1 items-center',
            )}>
                {children}
            </div>
        </header>
    )
}

Header.displayName = 'Header'
