import { useState, useEffect } from "react"
import { AuthenticatedLayout } from "@/layouts"
import { useTranslation } from 'react-i18next'
import { Main } from "@/components/layout/main"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { DashboardHeader } from "@/components/dashboard/resume/dashboard-header"
import { ResumeSidebar } from "@/components/dashboard/resume/sidebar"
import { ProfileSection } from "@/components/dashboard/resume/sections/profile-section"
import { SummarySection } from "@/components/dashboard/resume/sections/summary-section"
import { SkillsSection } from "@/components/dashboard/resume/sections/skills-section"
import { ExperienceSection } from "@/components/dashboard/resume/sections/experience-section"
import { EducationSection } from "@/components/dashboard/resume/sections/education-section"
import { LanguagesSection } from "@/components/dashboard/resume/sections/languages-section"
import { LoadingState } from "@/components/dashboard/resume/loading-state"
import { ResumeWizard } from "@/components/dashboard/resume/wizard/resume-wizard"
import axios from "axios"
import { toast } from "sonner"

export default function ResumeDashboard() {
  const { t } = useTranslation()
  // État pour stocker les données du CV
  const [resumeData, setResumeData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [showWizard, setShowWizard] = useState(false)

  // Charger les données du CV
  const fetchResumeData = async () => {
    setLoading(true)
    setError(false)

    try {
      console.log('Chargement des données du CV...')
      const response = await axios.get('/api/resume')
      console.log('Réponse de resume:', response.data)

      const resume = response.data.resume

      // S'assurer que toutes les relations existent
      resume.skills = resume.skills || []
      resume.experiences = resume.experiences || []
      resume.educations = resume.educations || []
      resume.languages = resume.languages || []

      setResumeData(resume)

      // Afficher un message d'erreur si présent
      if (response.data.error) {
        toast.warning(response.data.error)
      }

      // Déterminer si nous devons afficher le wizard
      // Si le CV est vide ou a un faible taux de complétion, afficher le wizard
      const isEmpty = !resume.summary &&
                     resume.skills.length === 0 &&
                     resume.experiences.length === 0 &&
                     resume.educations.length === 0 &&
                     resume.languages.length === 0

      const hasLowCompletion = resume.completion_percentage < 20

      setShowWizard(isEmpty || hasLowCompletion)
    } catch (error: any) {
      console.error("Erreur lors du chargement des données du CV:", error)

      // Gérer les erreurs
      console.error('Détails de l\'erreur:', error.response?.data)

      // Afficher un message d'erreur
      const errorMessage = error.response?.data?.message || "Erreur lors du chargement des données du CV"
      toast.error(errorMessage)

      setError(true)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchResumeData()
  }, [])

  return (
    <AuthenticatedLayout title={t("Résumé de profil")}>
      <Main>
        <div className="container mx-auto py-6">
          {/* Dashboard Header with stats */}
          <DashboardHeader />

          {/* Loading state or error */}
          <LoadingState
            isLoading={loading}
            error={error && !resumeData}
            onRetry={fetchResumeData}
          />

          {/* Main content when data is loaded */}
          {!loading && resumeData && (
            showWizard ? (
              <ResumeWizard
                resumeData={resumeData}
                onComplete={() => {
                  setShowWizard(false)
                  fetchResumeData()
                }}
              />
            ) : (
              <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                {/* Left sidebar with progress */}
                <div className="md:col-span-1">
                  <ResumeSidebar completionPercentage={resumeData.completion_percentage || 0} />
                </div>

                {/* Main content area */}
                <div className="md:col-span-3">
                  <Card className="p-6">
                    {/* Profile header */}
                    <ProfileSection resumeData={resumeData} onUpdate={fetchResumeData} />

                    <Separator className="my-6" />

                    {/* Resume section */}
                    <SummarySection resumeData={resumeData} onUpdate={fetchResumeData} />

                    {/* Competencies section */}
                    <SkillsSection resumeData={resumeData} onUpdate={fetchResumeData} />

                    {/* Experience section */}
                    <ExperienceSection resumeData={resumeData} onUpdate={fetchResumeData} />

                    {/* Education section */}
                    <EducationSection resumeData={resumeData} onUpdate={fetchResumeData} />

                    {/* Languages section */}
                    <LanguagesSection resumeData={resumeData} onUpdate={fetchResumeData} />
                  </Card>
                </div>
              </div>
            )
          )}
        </div>
      </Main>
    </AuthenticatedLayout>
  )
}
