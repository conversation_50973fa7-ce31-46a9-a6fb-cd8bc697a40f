<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('job_offers', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->unsignedBigInteger('company_id');
            $table->json('tags')->nullable();
            $table->text('description');
            $table->text('prerequisites')->nullable();
            $table->text('responsabilities')->nullable();
            $table->unsignedBigInteger('position_type_id');
            $table->string('city');
            $table->string('country');
            $table->dateTime('listedAt');
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies');
            $table->foreign('position_type_id')->references('id')->on('position_types');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('job_offers');
    }
}; 