'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { PositionType } from '../data/schema'
import axios from 'axios'
import { router } from '@inertiajs/react'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: PositionType
}

export function PositionTypesDeleteDialog({ open, onOpenChange, currentRow }: Props) {
  const [deleteInput, setDeleteInput] = useState('')
  const [loading, setLoading] = useState(false)

  const handleDelete = async () => {
    if (deleteInput !== currentRow.code) {
      toast({
        title: 'Erreur',
        description: 'Le code saisi ne correspond pas',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    try {
      await axios.delete(`/api/position-types/${currentRow.id}`)
      toast({
        title: 'Succès',
        description: 'Type de poste supprimé avec succès',
      })
      onOpenChange(false)
      setDeleteInput('')
      router.reload()
    } catch (error: any) {
      console.error('Error deleting position type:', error)
      toast({
        title: 'Erreur',
        description: error.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={(state) => {
        if (!loading) {
          setDeleteInput('')
          onOpenChange(state)
        }
      }}
      title="Supprimer le type de poste"
      desc="Cette action est irréversible. Le type de poste sera définitivement supprimé."
      confirmText={loading ? 'Suppression...' : 'Supprimer'}
      handleConfirm={handleDelete}
      disabled={deleteInput !== currentRow.code || loading}
      destructive={true}
    >
      <Alert variant='destructive'>
        <IconAlertTriangle className='h-4 w-4' />
        <AlertTitle>Attention !</AlertTitle>
        <AlertDescription>
          Cette action supprimera définitivement le type de poste &quot;{currentRow.name}&quot; ({currentRow.code}).
        </AlertDescription>
      </Alert>
      
      <div className='space-y-2'>
        <Label htmlFor="delete-input">
          Pour confirmer, tapez le code du type de poste : <strong>{currentRow.code}</strong>
        </Label>
        <Input
          id="delete-input"
          value={deleteInput}
          onChange={(e) => setDeleteInput(e.target.value)}
          placeholder="Tapez le code du type de poste"
          disabled={loading}
        />
      </div>
    </ConfirmDialog>
  )
}
